# 八闽助业集市 Brownfield 架构

## 现有技术架构概览

八闽助业集市采用多端分离的架构设计，支持管理后台、C端小程序、商家端小程序和H5端等多个客户端。

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    客户端层 (Client Layer)                    │
├─────────────────┬─────────────────┬─────────────────┬─────────┤
│   管理后台       │   C端用户小程序   │   商家端小程序   │  H5端   │
│  (Vue 2.6.10)   │  (UniApp+Vue3)  │  (UniApp+Vue3)  │(UniApp) │
│ Ant Design Vue  │     uv-ui       │     uv-ui       │ 微信SDK │
└─────────────────┴─────────────────┴─────────────────┴─────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    网关层 (Gateway Layer)                     │
│                      Nginx + SSL                            │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                   应用服务层 (Application Layer)              │
│              Spring Boot 2.x + jeecg-boot 3.5.x            │
│                     RESTful API Services                    │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                   数据访问层 (Data Access Layer)              │
│                    MyBatis Plus 3.5.x                      │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层 (Data Storage Layer)            │
├─────────────────────────────┬───────────────────────────────┤
│         MySQL 8.0           │          Redis 6.0            │
│      (主数据库)              │         (缓存层)               │
└─────────────────────────────┴───────────────────────────────┘
```

## 后端架构详解

### 技术栈组成
- **框架**: Spring Boot 2.x + jeecg-boot 3.5.x
- **ORM**: MyBatis Plus 3.5.x
- **数据库**: MySQL 8.0 (主库) + Redis 6.0 (缓存)
- **构建工具**: Maven 3.8.x
- **JDK版本**: OpenJDK 1.8
- **容器**: Docker (生产环境)

### 分层架构设计
```
Controller Layer (控制层)
    ├── FrontController     - 公开接口控制器
    ├── AfterController     - C端用户接口控制器  
    ├── BeforeController    - 商家端公开接口控制器
    ├── BackController      - 商家端认证接口控制器
    └── SysController       - 系统管理接口控制器

Service Layer (业务层)
    ├── UserService         - 用户业务服务
    ├── MerchantService     - 商家业务服务
    ├── GoodsService        - 商品业务服务
    ├── OrderService        - 订单业务服务
    └── FinanceService      - 财务业务服务

Repository Layer (数据访问层)
    ├── UserMapper          - 用户数据访问
    ├── MerchantMapper      - 商家数据访问
    ├── GoodsMapper         - 商品数据访问
    ├── OrderMapper         - 订单数据访问
    └── FinanceMapper       - 财务数据访问

Entity Layer (实体层)
    ├── UserInfo            - 用户实体
    ├── StoreInfo           - 店铺实体
    ├── GoodsInfo           - 商品实体
    ├── OrderInfo           - 订单实体
    └── FinanceLog          - 财务实体
```

### API路径设计规范
```
/front/     - 公开接口，无需认证
    ├── /user/register      - 用户注册
    ├── /user/login         - 用户登录
    ├── /goods/list         - 商品列表
    └── /category/list      - 分类列表

/after/     - C端用户接口，需要userToken认证
    ├── /user/info          - 用户信息
    ├── /order/create       - 创建订单
    ├── /order/list         - 订单列表
    └── /balance/recharge   - 余额充值

/before/    - 商家端公开接口
    ├── /merchant/register  - 商家注册
    └── /merchant/login     - 商家登录

/back/      - 商家端接口，需要merchantToken认证
    ├── /goods/manage       - 商品管理
    ├── /order/process      - 订单处理
    └── /finance/withdraw   - 提现申请

/sys/       - 系统管理接口，需要管理员权限
    ├── /user/manage        - 用户管理
    ├── /role/manage        - 角色管理
    └── /dict/manage        - 字典管理
```

### 认证授权机制
```java
// C端用户认证
@RequestHeader("Authorization") String userToken
LoginMemberUtil.getLoginMemberId() // 获取用户ID

// 商家端认证  
@RequestHeader("merchantToken") String merchantToken
@RequestHeader("X-App-Type") String appType // "merchant"
request.getAttribute("sysUserId") // 获取商家ID

// 管理后台认证
@RequestHeader("X-Access-Token") String accessToken
SecurityUtils.getSubject().getPrincipal() // 获取管理员信息
```

## 前端架构详解

### 管理后台架构 (heartful-mall-web)
```
技术栈:
├── Vue 2.6.10              - 核心框架
├── Ant Design Vue 1.7.2    - UI组件库
├── axios 0.18.0            - HTTP客户端
├── Vuex                    - 状态管理
└── Vue Router              - 路由管理

项目结构:
src/
├── components/             - 公共组件
├── views/                  - 页面组件
├── store/                  - Vuex状态管理
├── router/                 - 路由配置
├── api/                    - API接口
├── utils/                  - 工具函数
└── assets/                 - 静态资源

关键特性:
├── JeecgListMixin         - 列表页面混入
├── a-form-model           - 表单验证组件
├── j-dict-select-tag      - 字典选择组件
└── v-has                  - 权限控制指令
```

### 小程序架构 (UniApp + Vue 3)
```
技术栈:
├── UniApp                  - 跨端框架
├── Vue 3 + Composition API - 核心框架
├── Pinia                   - 状态管理
├── luch-request           - 网络请求库
└── uv-ui                  - UI组件库

项目结构:
├── pages/                  - 页面文件
├── components/             - 组件文件
├── store/                  - Pinia状态管理
├── api/                    - API接口
├── utils/                  - 工具函数
├── static/                 - 静态资源
├── uni_modules/            - uni模块
├── manifest.json           - 应用配置
└── pages.json             - 页面配置

设计规范:
├── 品牌色: #667eea
├── 卡片圆角: 16rpx
├── 组件圆角: 12rpx
└── 基础间距: 16rpx
```

### H5端特殊配置
```
技术特性:
├── 微信JS SDK集成         - 微信功能支持
├── 企业微信对接           - 企业微信登录
├── 微信环境检测           - 运行环境判断
└── 分享功能              - 微信分享

配置文件:
├── manifest.json          - H5专用配置
├── wxconfig.js           - 微信SDK配置
└── share.js              - 分享功能配置
```

## 数据库架构设计

### 数据库设计原则
```sql
-- 基础字段规范（所有表必须包含）
CREATE TABLE example_table (
    id VARCHAR(32) PRIMARY KEY COMMENT '主键ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(32) COMMENT '创建人',
    update_by VARCHAR(32) COMMENT '更新人',
    del_flag TINYINT(1) DEFAULT 0 COMMENT '删除标志(0-正常,1-删除)',
    -- 业务字段...
    INDEX idx_create_time (create_time),
    INDEX idx_del_flag (del_flag)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='示例表';
```

### 核心数据表结构
```sql
-- 用户相关表
user_info           - 用户基础信息
user_balance        - 用户余额
user_balance_log    - 余额变动日志
user_address        - 收货地址

-- 商家相关表  
store_info          - 店铺信息
store_goods         - 商品信息
store_order         - 订单信息
store_finance       - 财务记录

-- 系统相关表
sys_user            - 系统用户
sys_role            - 系统角色
sys_permission      - 系统权限
sys_dict            - 数据字典

-- 业务相关表
goods_category      - 商品分类
order_info          - 订单信息
order_item          - 订单明细
payment_record      - 支付记录
```

### 金额字段规范
```sql
-- 所有金额字段统一使用decimal类型
balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '余额'
price DECIMAL(10,2) NOT NULL COMMENT '价格'
amount DECIMAL(10,2) NOT NULL COMMENT '金额'
```

## 缓存架构设计

### Redis缓存策略
```
缓存层次:
├── L1: 应用内存缓存 (Caffeine)
├── L2: Redis分布式缓存
└── L3: 数据库持久化存储

缓存模式:
├── Cache-Aside     - 旁路缓存模式
├── Write-Through   - 写透模式
└── Write-Behind    - 写回模式

缓存分类:
├── 用户会话缓存    - 30分钟过期
├── 商品信息缓存    - 1小时过期
├── 热点数据缓存    - 24小时过期
└── 配置信息缓存    - 永不过期(手动更新)
```

### 缓存键命名规范
```
user:info:{userId}          - 用户信息
goods:detail:{goodsId}      - 商品详情
order:info:{orderId}        - 订单信息
category:list               - 分类列表
config:dict:{dictCode}      - 字典配置
```

## 安全架构设计

### 认证安全
```
JWT Token设计:
├── Header: 算法类型
├── Payload: 用户信息 + 过期时间
└── Signature: 签名验证

Token管理:
├── Access Token: 2小时有效期
├── Refresh Token: 7天有效期
└── 自动刷新机制
```

### 数据安全
```
加密策略:
├── 密码: BCrypt加密存储
├── 敏感数据: AES对称加密
├── 传输: HTTPS + TLS 1.2
└── 数据库: 透明数据加密(TDE)

权限控制:
├── RBAC权限模型
├── 接口级权限验证
├── 数据级权限控制
└── 操作日志记录
```

## 性能优化架构

### 数据库优化
```sql
-- 索引优化策略
CREATE INDEX idx_user_phone ON user_info(phone);
CREATE INDEX idx_order_status_time ON order_info(status, create_time);
CREATE INDEX idx_goods_category_status ON goods_info(category_id, status);

-- 分页查询优化
SELECT * FROM order_info 
WHERE create_time >= '2024-01-01' 
ORDER BY create_time DESC 
LIMIT 20 OFFSET 0;
```

### 应用层优化
```java
// 异步处理
@Async
public void processOrderAsync(String orderId) {
    // 异步处理订单
}

// 缓存注解
@Cacheable(value = "goods", key = "#goodsId")
public GoodsInfo getGoodsById(String goodsId) {
    return goodsMapper.selectById(goodsId);
}

// 批量操作
@Transactional
public void batchUpdateGoods(List<GoodsInfo> goodsList) {
    goodsService.updateBatchById(goodsList);
}
```

## 监控和运维架构

### 应用监控
```
监控指标:
├── 应用性能: 响应时间、吞吐量、错误率
├── 系统资源: CPU、内存、磁盘、网络
├── 数据库: 连接数、慢查询、锁等待
└── 缓存: 命中率、内存使用、连接数

监控工具:
├── Spring Boot Actuator - 应用健康检查
├── Micrometer - 指标收集
├── Prometheus - 指标存储
└── Grafana - 可视化展示
```

### 日志管理
```
日志分类:
├── 访问日志: Nginx access.log
├── 应用日志: application.log
├── 错误日志: error.log
└── 业务日志: business.log

日志格式:
[时间] [级别] [线程] [类名] - [消息] [异常堆栈]
```

## 部署架构

### 环境配置
```
开发环境 (Development):
├── 单机部署
├── H2内存数据库
└── 本地Redis

测试环境 (Testing):  
├── Docker容器部署
├── MySQL测试库
└── Redis集群

生产环境 (Production):
├── Kubernetes集群
├── MySQL主从复制
├── Redis哨兵模式
└── Nginx负载均衡
```

### 持续集成/持续部署 (CI/CD)
```
流水线:
代码提交 → 自动构建 → 单元测试 → 代码扫描 → 
镜像构建 → 部署测试环境 → 集成测试 → 
部署生产环境 → 健康检查 → 监控告警
```

## 架构改进计划

### 短期改进 (1-3个月)
1. **性能优化**
   - 数据库查询优化
   - 缓存策略完善
   - 接口响应时间优化

2. **监控完善**
   - 完善监控指标
   - 增加告警规则
   - 优化日志记录

### 中期改进 (3-6个月)
1. **架构升级**
   - 微服务架构改造
   - 服务注册发现
   - 配置中心建设

2. **容器化**
   - Docker容器化
   - Kubernetes编排
   - 自动扩缩容

### 长期改进 (6-12个月)
1. **云原生**
   - 服务网格(Service Mesh)
   - 无服务器架构(Serverless)
   - 多云部署

2. **智能化**
   - AI推荐系统
   - 智能运维
   - 自动化测试

通过持续的架构优化和技术升级，八闽助业集市将构建更加稳定、高效、可扩展的技术架构，为业务发展提供强有力的技术支撑。
