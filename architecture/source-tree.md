# 八闽助业集市项目源码结构

## 项目总览

八闽助业集市采用多根工作区架构，包含5个独立子项目和1个共享文档中心。

```
heartful-mall/                          # 工作区根目录
├── .bmad-core/                         # BMAD工作区级配置
├── docs/                               # 共享文档中心
├── heartful-mall-backend/              # 后端API服务
├── heartful-mall-web/                  # 管理后台
├── heartful-mall-app/                  # C端用户小程序
├── heartful-mall-merchants-pro/        # 商家端小程序
├── heartful-mall-h5/                   # 公众号H5端
└── heartful-mall.code-workspace        # VSCode工作区配置
```

## 后端项目结构 (heartful-mall-backend)

### 技术特征
- **框架**: Spring Boot + jeecg-boot
- **构建**: Maven
- **特征文件**: pom.xml, application.yml

### 目录结构
```
heartful-mall-backend/
├── .bmad-core/                         # BMAD项目级配置
├── docs/                               # 后端项目文档
│   ├── architecture/                   # 架构文档
│   │   ├── api-specs.md               # API规范文档
│   │   ├── endpoints.md               # 接口端点文档
│   │   ├── database-schema.md         # 数据库设计文档
│   │   └── business-logic.md          # 业务逻辑文档
│   └── stories/                       # Story文档
├── jeecg-boot/                        # jeecg-boot主模块
│   ├── jeecg-boot-base-core/          # 核心基础模块
│   ├── jeecg-module-system/           # 系统管理模块
│   └── jeecg-server-cloud/            # 云服务模块
├── jeecg-module-demo/                 # 示例模块
├── src/main/java/                     # Java源码
│   └── org/jeecg/modules/
│       ├── system/                    # 系统管理
│       ├── user/                      # 用户管理
│       ├── store/                     # 店铺管理
│       ├── goods/                     # 商品管理
│       ├── order/                     # 订单管理
│       └── finance/                   # 财务管理
├── src/main/resources/                # 资源文件
│   ├── application.yml                # 应用配置
│   ├── application-dev.yml            # 开发环境配置
│   └── mapper/                        # MyBatis映射文件
├── database/                          # 数据库脚本
├── logs/                              # 日志文件
├── upload/                            # 上传文件
└── pom.xml                            # Maven配置文件
```

### 核心模块职责
- **system**: 系统用户、角色、权限管理
- **user**: C端用户管理、认证、余额
- **store**: 商家店铺管理、认证
- **goods**: 商品信息、分类、库存管理
- **order**: 订单创建、支付、状态管理
- **finance**: 财务统计、资金流水

## 管理后台结构 (heartful-mall-web)

### 技术特征
- **框架**: Vue 2.6.10 + Ant Design Vue 1.7.2
- **构建**: Vue CLI
- **特征文件**: package.json, vue.config.js

### 目录结构
```
heartful-mall-web/
├── .bmad-core/                         # BMAD项目级配置
├── docs/                               # 管理后台文档
│   ├── architecture/                   # 架构文档
│   │   ├── api-integration.md         # API集成文档
│   │   ├── component-standards.md     # 组件规范文档
│   │   └── admin-features.md          # 管理功能文档
│   └── stories/                       # Story文档
├── public/                            # 静态资源
├── src/                               # 源码目录
│   ├── api/                           # API接口定义
│   │   ├── system.js                  # 系统管理API
│   │   ├── user.js                    # 用户管理API
│   │   ├── store.js                   # 店铺管理API
│   │   └── order.js                   # 订单管理API
│   ├── components/                    # 公共组件
│   │   ├── jeecg/                     # jeecg组件
│   │   └── common/                    # 通用组件
│   ├── views/                         # 页面组件
│   │   ├── system/                    # 系统管理页面
│   │   ├── user/                      # 用户管理页面
│   │   ├── store/                     # 店铺管理页面
│   │   ├── goods/                     # 商品管理页面
│   │   └── order/                     # 订单管理页面
│   ├── router/                        # 路由配置
│   ├── store/                         # Vuex状态管理
│   ├── utils/                         # 工具函数
│   ├── mixins/                        # 混入
│   │   └── JeecgListMixin.js          # 列表页面混入
│   ├── assets/                        # 静态资源
│   ├── config/                        # 配置文件
│   └── main.js                        # 入口文件
├── dist/                              # 构建输出
├── package.json                       # npm配置
└── vue.config.js                      # Vue CLI配置
```

### 页面模块职责
- **system**: 系统用户、角色、权限、菜单管理
- **user**: C端用户管理、余额管理、等级管理
- **store**: 商家审核、店铺管理、等级管理
- **goods**: 商品审核、分类管理、库存监控
- **order**: 订单监控、售后处理、数据统计

## C端用户小程序结构 (heartful-mall-app)

### 技术特征
- **框架**: UniApp + Vue 3 + Composition API
- **UI库**: uv-ui
- **状态管理**: Pinia
- **特征文件**: manifest.json, pages.json

### 目录结构
```
heartful-mall-app/
├── .bmad-core/                         # BMAD项目级配置
├── docs/                               # C端小程序文档
│   ├── architecture/                   # 架构文档
│   │   ├── api-integration.md         # API集成文档
│   │   ├── state-management.md        # 状态管理文档
│   │   └── user-features.md           # 用户功能文档
│   └── stories/                       # Story文档
├── components/                        # 公共组件
│   ├── common/                        # 通用组件
│   └── business/                      # 业务组件
├── pages/                             # 页面目录
│   ├── index/                         # 首页
│   ├── goods/                         # 商品相关
│   ├── cart/                          # 购物车
│   ├── order/                         # 订单相关
│   └── profile/                       # 个人中心
├── packageUser/                       # 用户功能包
│   ├── login/                         # 登录注册
│   ├── profile/                       # 个人资料
│   └── balance/                       # 余额管理
├── packageGoods/                      # 商品功能包
│   ├── list/                          # 商品列表
│   ├── detail/                        # 商品详情
│   └── search/                        # 商品搜索
├── packageOrder/                      # 订单功能包
│   ├── create/                        # 创建订单
│   ├── list/                          # 订单列表
│   └── detail/                        # 订单详情
├── packageCart/                       # 购物车功能包
├── packageRecharge/                   # 充值功能包
├── store/                             # Pinia状态管理
│   ├── user.js                        # 用户状态
│   ├── goods.js                       # 商品状态
│   └── cart.js                        # 购物车状态
├── utils/                             # 工具函数
│   ├── request.js                     # 网络请求
│   ├── auth.js                        # 认证工具
│   └── common.js                      # 通用工具
├── static/                            # 静态资源
├── uni_modules/                       # uni_modules插件
├── manifest.json                      # 应用配置
├── pages.json                         # 页面配置
└── App.vue                            # 应用入口
```

### 功能包职责
- **packageUser**: 用户登录、注册、个人信息管理
- **packageGoods**: 商品浏览、搜索、详情查看
- **packageOrder**: 订单创建、支付、查看、售后
- **packageCart**: 购物车管理、商品收藏
- **packageRecharge**: 余额充值、充值记录

## 商家端小程序结构 (heartful-mall-merchants-pro)

### 技术特征
- **框架**: UniApp + Vue 3 + Composition API
- **状态管理**: Pinia
- **特征文件**: manifest.json, pages.json (商家专用页面)

### 目录结构
```
heartful-mall-merchants-pro/
├── .bmad-core/                         # BMAD项目级配置
├── docs/                               # 商家端文档
│   ├── architecture/                   # 架构文档
│   │   ├── api-integration.md         # API集成文档
│   │   ├── merchant-features.md       # 商家功能文档
│   │   └── merchant-workflow.md       # 商家工作流文档
│   └── stories/                       # Story文档
├── components/                        # 公共组件
├── pages/                             # 页面目录
│   ├── index/                         # 商家首页
│   ├── login/                         # 商家登录
│   └── profile/                       # 商家资料
├── packageGoods/                      # 商品管理包
│   ├── list/                          # 商品列表
│   ├── add/                           # 添加商品
│   ├── edit/                          # 编辑商品
│   └── category/                      # 商品分类
├── packageOrder/                      # 订单管理包
│   ├── list/                          # 订单列表
│   ├── detail/                        # 订单详情
│   └── process/                       # 订单处理
├── packageFinance/                    # 财务管理包
│   ├── overview/                      # 财务概览
│   ├── income/                        # 收入明细
│   └── withdraw/                      # 提现管理
├── packageUserSecurity/               # 用户安全包
│   ├── profile/                       # 个人资料
│   └── security/                      # 安全设置
├── store/                             # Pinia状态管理
│   ├── merchant.js                    # 商家状态
│   ├── goods.js                       # 商品状态
│   └── order.js                       # 订单状态
├── utils/                             # 工具函数
├── static/                            # 静态资源
├── manifest.json                      # 应用配置
├── pages.json                         # 页面配置
└── App.vue                            # 应用入口
```

### 功能包职责
- **packageGoods**: 商品CRUD、分类管理、库存管理
- **packageOrder**: 订单处理、发货管理、售后处理
- **packageFinance**: 收入统计、提现管理、财务报表
- **packageUserSecurity**: 商家资料、安全设置

## H5端项目结构 (heartful-mall-h5)

### 技术特征
- **框架**: UniApp + Vue 3 (H5专用)
- **集成**: 微信JS SDK + 企业微信
- **特征文件**: manifest.json (H5专用配置)

### 目录结构
```
heartful-mall-h5/
├── .bmad-core/                         # BMAD项目级配置
├── docs/                               # H5端文档
│   ├── architecture/                   # 架构文档
│   │   ├── api-integration.md         # API集成文档
│   │   ├── wechat-integration.md      # 微信集成文档
│   │   └── h5-specific.md             # H5特定功能文档
│   └── stories/                       # Story文档
├── components/                        # 公共组件
├── pages/                             # 页面目录
│   ├── index/                         # H5首页
│   ├── goods/                         # 商品页面
│   ├── user/                          # 用户页面
│   └── share/                         # 分享页面
├── utils/                             # 工具函数
│   ├── wechat.js                      # 微信SDK工具
│   ├── enterprise.js                  # 企业微信工具
│   └── share.js                       # 分享工具
├── store/                             # 状态管理
├── static/                            # 静态资源
├── manifest.json                      # H5应用配置
├── pages.json                         # 页面配置
└── App.vue                            # 应用入口
```

### H5特色功能
- **微信登录**: 微信授权登录
- **微信分享**: 朋友圈、好友分享
- **微信支付**: 微信支付集成
- **企业微信**: 企业微信对接

## 共享文档中心 (docs/)

### 目录结构
```
docs/
├── architecture/                       # 工作区级架构文档
│   ├── coding-standards.md            # 编码规范
│   ├── tech-stack.md                  # 技术栈说明
│   └── source-tree.md                 # 源码结构说明
├── 开发规范/                           # 开发规范文档
│   ├── API路径规范.md                  # API路径规范
│   ├── 后端API开发规范.md              # 后端开发规范
│   ├── 接口对接验证流程.md             # 接口验证流程
│   └── 质量保证要求.md                 # 质量保证要求
├── 技术方案/                           # 技术方案文档
│   ├── BMAD多根工作区全栈开发最佳实践.md
│   └── 八闽助业集市BMAD多根工作区全栈开发实施方案.md
├── 需求说明/                           # 需求文档
├── sql/                                # 数据库脚本
│   ├── 增量脚本/                       # 增量更新脚本
│   └── 测试脚本/                       # 测试数据脚本
├── tasks/                              # 任务计划文档
├── api-standards.md                    # API标准规范
├── error-handling.md                   # 错误处理规范
├── coding-standards.md                 # 编码标准
├── security-standards.md               # 安全标准
└── data-flow.md                        # 数据流设计
```

## BMAD配置结构

### 工作区级配置 (.bmad-core/)
```
.bmad-core/
├── core-config.yaml                    # 核心配置文件
├── data/
│   └── technical-preferences.md        # 技术偏好配置
├── agents/                             # AI代理配置
├── tasks/                              # 任务模板
├── templates/                          # 文档模板
└── workflows/                          # 工作流配置
```

### 子项目级配置
每个子项目都有独立的`.bmad-core/`配置，包含：
- **core-config.yaml**: 项目专用配置
- **devLoadAlwaysFiles**: 自动加载的文档列表
- **项目特定配置**: 如API路径、认证机制等

## 项目间依赖关系

```
┌─────────────────┐    ┌─────────────────┐
│  管理后台        │    │  C端用户小程序   │
│  heartful-mall- │    │  heartful-mall- │
│  web            │    │  app            │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          │    ┌─────────────────┐│
          │    │  商家端小程序    ││
          │    │  heartful-mall- ││
          │    │  merchants-pro  ││
          │    └─────────┬───────┘│
          │              │        │
          │    ┌─────────┴───────┐│
          │    │  H5端           ││
          │    │  heartful-mall- ││
          │    │  h5             ││
          │    └─────────┬───────┘│
          │              │        │
          └──────────────┼────────┘
                         │
          ┌──────────────┴────────────┐
          │     后端API服务            │
          │     heartful-mall-backend │
          └───────────────────────────┘
```

## 开发工作流

### 跨项目开发流程
1. **需求分析** - 确定涉及的子项目
2. **Story创建** - 创建跨端Story
3. **API设计** - 后端API接口设计
4. **前端开发** - 各前端项目并行开发
5. **集成测试** - 跨项目集成测试
6. **部署发布** - 按依赖顺序发布

### 文档同步机制
- **BMAD自动加载** - 跨项目文档自动加载
- **版本控制** - Git统一版本管理
- **文档更新** - 实时同步更新

本源码结构支持高效的多端协同开发，确保项目的可维护性和扩展性。
