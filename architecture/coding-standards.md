# 八闽助业集市编码规范

## 概述

本文档定义了八闽助业集市项目的统一编码规范，适用于所有子项目的开发工作。

## Java后端编码规范 (heartful-mall-backend)

### 基础规范
- 遵循阿里巴巴Java开发规范
- 使用UTF-8编码
- 缩进使用4个空格，不使用Tab
- 行长度不超过120字符

### 命名规范
```java
// 类名：大驼峰命名法
public class UserService {
    
    // 方法名：小驼峰命名法
    public void getUserInfo() {}
    
    // 常量：全大写，下划线分隔
    private static final String DEFAULT_STATUS = "ACTIVE";
    
    // 变量：小驼峰命名法
    private String userName;
}
```

### 注解使用
```java
// Controller层
@RestController
@RequestMapping("/after/user")
@Api(tags = "用户管理")
public class UserController {
    
    @PostMapping("/info")
    @ApiOperation("获取用户信息")
    public Result<UserVO> getUserInfo() {
        // 实现逻辑
    }
}

// Service层
@Service
@Transactional(rollbackFor = Exception.class)
public class UserServiceImpl implements UserService {
    // 实现逻辑
}
```

### 异常处理
```java
// 统一异常处理
try {
    // 业务逻辑
} catch (BusinessException e) {
    log.error("业务异常：{}", e.getMessage(), e);
    throw e;
} catch (Exception e) {
    log.error("系统异常：{}", e.getMessage(), e);
    throw new BusinessException("系统异常，请稍后重试");
}
```

## Vue2管理后台编码规范 (heartful-mall-web)

### 组件结构
```vue
<template>
  <div class="user-management">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form-model layout="inline" :model="queryParam" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-model-item label="用户名">
              <a-input v-model="queryParam.username" placeholder="请输入用户名"/>
            </a-form-model-item>
          </a-col>
        </a-row>
      </a-form-model>
    </div>
    
    <!-- 表格区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
    </div>
    
    <a-table
      ref="table"
      size="middle"
      :columns="columns"
      :dataSource="dataSource"
      :pagination="ipagination"
      :loading="loading"
      @change="handleTableChange">
    </a-table>
  </div>
</template>

<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'

export default {
  name: 'UserManagement',
  mixins: [JeecgListMixin],
  data() {
    return {
      // 查询参数
      queryParam: {},
      // 表格列定义
      columns: [
        {
          title: '用户名',
          align: 'center',
          dataIndex: 'username'
        }
      ]
    }
  },
  methods: {
    // 新增用户
    handleAdd() {
      this.$refs.modalForm.add()
    }
  }
}
</script>
```

### 表单验证规范
```javascript
// 使用a-form-model进行表单验证
data() {
  return {
    form: {},
    rules: {
      username: [
        { required: true, message: '请输入用户名', trigger: 'blur' },
        { min: 2, max: 20, message: '用户名长度在2到20个字符', trigger: 'blur' }
      ],
      email: [
        { required: true, message: '请输入邮箱', trigger: 'blur' },
        { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
      ]
    }
  }
}
```

## Vue3 UniApp编码规范 (小程序项目)

### Composition API结构
```vue
<template>
  <view class="user-profile">
    <view class="profile-header">
      <image :src="userInfo.avatar" class="avatar"></image>
      <text class="username">{{ userInfo.username }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useUserStore } from '@/store/user'

// 响应式数据
const userInfo = reactive({
  avatar: '',
  username: ''
})

// 状态管理
const userStore = useUserStore()

// 生命周期
onMounted(() => {
  loadUserInfo()
})

// 方法定义
const loadUserInfo = async () => {
  try {
    const result = await userStore.getUserInfo()
    Object.assign(userInfo, result)
  } catch (error) {
    uni.showToast({
      title: '获取用户信息失败',
      icon: 'none'
    })
  }
}
</script>

<style lang="scss" scoped>
.user-profile {
  padding: 32rpx;
  
  .profile-header {
    display: flex;
    align-items: center;
    
    .avatar {
      width: 120rpx;
      height: 120rpx;
      border-radius: 60rpx;
    }
    
    .username {
      margin-left: 24rpx;
      font-size: 32rpx;
      font-weight: 500;
    }
  }
}
</style>
```

### 网络请求规范
```javascript
// 使用luch-request
import { http } from '@/utils/request'

// API调用
export const getUserInfo = () => {
  return http.get('/after/user/info')
}

export const updateUserInfo = (data) => {
  return http.post('/after/user/update', data)
}
```

## 数据库编码规范

### 表结构规范
```sql
-- 表名使用下划线命名法
CREATE TABLE `user_info` (
  `id` varchar(32) NOT NULL COMMENT '主键ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1-正常，0-禁用',
  `balance` decimal(10,2) DEFAULT '0.00' COMMENT '余额（必须使用decimal）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(32) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(32) DEFAULT NULL COMMENT '更新人',
  `del_flag` tinyint(1) DEFAULT '0' COMMENT '删除标志：0-正常，1-删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息表';
```

### 字段命名规范
- 主键：统一使用`id`
- 时间字段：`create_time`、`update_time`
- 操作人字段：`create_by`、`update_by`
- 删除标志：`del_flag`
- 状态字段：`status`
- 金额字段：必须使用`decimal`类型

## API接口规范

### 路径规范
```
/front/     - 公开接口（无需认证）
/after/     - C端用户认证接口
/before/    - 商家端公开接口
/back/      - 商家端认证接口
/sys/       - 系统管理接口
```

### 响应格式规范
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "result": {
    "data": "具体数据"
  },
  "timestamp": 1640995200000
}
```

### 错误码规范
```
200 - 成功
400 - 参数错误
401 - 未认证
403 - 无权限
404 - 资源不存在
429 - 请求频繁
500 - 服务器错误
```

## 注释规范

### Java注释
```java
/**
 * 用户服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface UserService {
    
    /**
     * 获取用户信息
     * 
     * @param userId 用户ID
     * @return 用户信息
     * @throws BusinessException 业务异常
     */
    UserVO getUserInfo(String userId) throws BusinessException;
}
```

### Vue注释
```vue
<script>
/**
 * 用户管理页面
 * 
 * @description 提供用户的增删改查功能
 * <AUTHOR>
 * @since 2024-01-01
 */
export default {
  name: 'UserManagement',
  // 组件实现
}
</script>
```

## 版本控制规范

### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 分支命名规范
```
master - 主分支
develop - 开发分支
feature/功能名 - 功能分支
hotfix/修复内容 - 热修复分支
```

## 性能优化规范

### 前端性能
- 图片懒加载
- 组件按需加载
- 合理使用缓存
- 避免内存泄漏

### 后端性能
- 数据库查询优化
- 合理使用索引
- 缓存策略
- 异步处理

## 安全规范

### 数据安全
- 敏感数据加密存储
- SQL注入防护
- XSS攻击防护
- CSRF攻击防护

### 接口安全
- 认证机制
- 权限控制
- 请求频率限制
- 数据校验

## 测试规范

### 单元测试
- 覆盖率不低于70%
- 关键业务逻辑必须测试
- 异常情况测试

### 集成测试
- API接口测试
- 数据库操作测试
- 跨系统集成测试

本规范将持续更新，所有开发人员必须严格遵守。
