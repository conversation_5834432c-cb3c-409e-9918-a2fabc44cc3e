# 八闽助业集市技术栈架构

## 项目概述

八闽助业集市采用微服务架构，包含1个后端服务和4个前端应用，支持多端用户访问。

## 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    八闽助业集市技术架构                        │
├─────────────────────────────────────────────────────────────┤
│  前端应用层                                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 管理后台     │ │ C端小程序    │ │ 商家端小程序 │ │ H5端    │ │
│  │ Vue2+AntD   │ │ UniApp+Vue3 │ │ UniApp+Vue3 │ │ UniApp  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│  API网关层                                                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ Spring Boot + jeecg-boot (统一API服务)                  │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│  数据存储层                                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │ MySQL 8.0   │ │ Redis 6.0   │ │ 文件存储     │            │
│  │ 主数据库     │ │ 缓存/会话   │ │ 图片/文档    │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
└─────────────────────────────────────────────────────────────┘
```

## 后端技术栈 (heartful-mall-backend)

### 核心框架
- **Spring Boot 2.7.x** - 微服务基础框架
- **jeecg-boot 3.5.x** - 快速开发平台
- **MyBatis Plus 3.5.x** - ORM框架
- **Spring Security** - 安全框架

### 数据库技术
- **MySQL 8.0** - 主数据库
- **Redis 6.0** - 缓存数据库
- **HikariCP** - 数据库连接池

### 开发工具
- **Maven 3.8.x** - 项目构建工具
- **JDK 1.8** - Java开发环境
- **Swagger 3.x** - API文档生成

### 部署技术
- **Docker** - 容器化部署
- **Nginx** - 反向代理
- **Linux** - 服务器环境

## 前端技术栈详解

### 管理后台 (heartful-mall-web)

#### 核心技术
```json
{
  "framework": "Vue 2.6.10",
  "ui": "Ant Design Vue 1.7.2",
  "state": "Vuex 3.x",
  "router": "Vue Router 3.x",
  "http": "axios 0.18.0",
  "build": "Vue CLI 4.x"
}
```

#### 特色功能
- **JeecgListMixin** - 列表页面混入
- **a-form-model** - 表单验证组件
- **j-dict-select-tag** - 字典选择组件
- **v-has** - 权限控制指令

#### 构建配置
```javascript
// vue.config.js
module.exports = {
  publicPath: '/',
  outputDir: 'dist',
  productionSourceMap: false,
  configureWebpack: {
    resolve: {
      alias: {
        '@': resolve('src')
      }
    }
  }
}
```

### C端用户小程序 (heartful-mall-app)

#### 核心技术
```json
{
  "framework": "UniApp + Vue 3",
  "composition": "Composition API",
  "ui": "uv-ui",
  "state": "Pinia",
  "http": "luch-request",
  "build": "HBuilderX"
}
```

#### 设计系统
```scss
// 设计规范
$primary-color: #667eea;
$card-radius: 16rpx;
$component-radius: 12rpx;
$base-spacing: 16rpx;
```

#### API配置
```javascript
// API路径配置
const API_CONFIG = {
  public: '/front/',      // 公开接口
  userAuth: '/after/'     // 用户认证接口
}
```

### 商家端小程序 (heartful-mall-merchants-pro)

#### 核心技术
```json
{
  "framework": "UniApp + Vue 3",
  "composition": "Composition API", 
  "ui": "uv-ui",
  "state": "Pinia",
  "http": "luch-request",
  "build": "HBuilderX"
}
```

#### 特色功能
- **商品管理** - 商品CRUD操作
- **订单处理** - 订单状态管理
- **数据统计** - 销售数据分析
- **客户服务** - 客服聊天功能
- **财务管理** - 收支明细管理

#### API配置
```javascript
// API路径配置
const API_CONFIG = {
  merchantPublic: '/before/',  // 商家公开接口
  merchantAuth: '/back/'       // 商家认证接口
}
```

### H5端 (heartful-mall-h5)

#### 核心技术
```json
{
  "framework": "UniApp + Vue 3",
  "composition": "Composition API",
  "wechat": "微信JS SDK",
  "enterprise": "企业微信SDK",
  "http": "luch-request",
  "build": "HBuilderX"
}
```

#### 微信集成
```javascript
// 微信JS SDK配置
wx.config({
  debug: false,
  appId: 'your-app-id',
  timestamp: timestamp,
  nonceStr: nonceStr,
  signature: signature,
  jsApiList: [
    'onMenuShareTimeline',
    'onMenuShareAppMessage',
    'chooseWXPay'
  ]
})
```

## 数据库架构

### MySQL数据库设计

#### 核心表结构
```sql
-- 用户相关表
user_info           -- 用户基础信息
user_balance        -- 用户余额
user_balance_log    -- 余额变动日志

-- 商家相关表  
store_info          -- 店铺信息
store_goods         -- 商品信息
store_order         -- 订单信息

-- 系统相关表
sys_user            -- 系统用户
sys_role            -- 系统角色
sys_permission      -- 系统权限
```

#### 数据类型规范
```sql
-- 金额字段统一使用decimal
balance DECIMAL(10,2) DEFAULT 0.00 COMMENT '余额'

-- 时间字段统一格式
create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'

-- 状态字段统一使用tinyint
status TINYINT(1) DEFAULT 1 COMMENT '状态：1-正常，0-禁用'
del_flag TINYINT(1) DEFAULT 0 COMMENT '删除标志：0-正常，1-删除'
```

### Redis缓存架构

#### 缓存策略
```
用户会话：user:session:{userId}
用户信息：user:info:{userId}
商品信息：goods:info:{goodsId}
订单缓存：order:cache:{orderId}
系统配置：sys:config:{configKey}
```

#### 过期策略
```
会话缓存：30分钟
用户信息：1小时
商品信息：30分钟
订单缓存：10分钟
系统配置：24小时
```

## API架构设计

### 路径规范
```
/front/     - 公开接口（无需认证）
├── /user/register      - 用户注册
├── /goods/list         - 商品列表
└── /store/info         - 店铺信息

/after/     - C端用户认证接口
├── /user/info          - 用户信息
├── /order/create       - 创建订单
└── /balance/recharge   - 余额充值

/before/    - 商家端公开接口
├── /merchant/register  - 商家注册
└── /goods/categories   - 商品分类

/back/      - 商家端认证接口
├── /goods/manage       - 商品管理
├── /order/process      - 订单处理
└── /finance/report     - 财务报表

/sys/       - 系统管理接口
├── /user/manage        - 用户管理
├── /role/manage        - 角色管理
└── /config/manage      - 配置管理
```

### 认证机制
```javascript
// C端用户认证
headers: {
  'Authorization': 'Bearer ' + userToken
}

// 商家端认证
headers: {
  'Authorization': 'Bearer ' + merchantToken,
  'X-App-Type': 'merchant'
}

// 管理后台认证
headers: {
  'X-Access-Token': adminToken
}
```

## 开发环境配置

### 开发工具版本
```
Node.js: 16.x+
npm: 8.x+
Java: JDK 1.8
Maven: 3.8.x
MySQL: 8.0
Redis: 6.0
HBuilderX: 3.8.x+
```

### 环境配置
```yaml
# 开发环境
spring:
  profiles:
    active: dev
  datasource:
    url: *******************************************************************************************************
    username: root
    password: password
  redis:
    host: localhost
    port: 6379
    database: 0
```

## 部署架构

### 生产环境部署
```
┌─────────────────────────────────────────┐
│ Nginx (负载均衡 + 静态资源)              │
├─────────────────────────────────────────┤
│ Spring Boot Application (Docker容器)    │
├─────────────────────────────────────────┤
│ MySQL Master-Slave (主从复制)           │
├─────────────────────────────────────────┤
│ Redis Cluster (集群模式)                │
└─────────────────────────────────────────┘
```

### Docker配置
```dockerfile
# Dockerfile
FROM openjdk:8-jre-alpine
VOLUME /tmp
COPY target/heartful-mall-backend.jar app.jar
ENTRYPOINT ["java","-jar","/app.jar"]
```

## 性能优化策略

### 前端优化
- **代码分割** - 路由懒加载
- **资源压缩** - Gzip压缩
- **缓存策略** - 浏览器缓存
- **图片优化** - WebP格式

### 后端优化
- **数据库优化** - 索引优化
- **缓存策略** - Redis缓存
- **连接池** - HikariCP配置
- **异步处理** - @Async注解

### 数据库优化
- **读写分离** - 主从复制
- **分库分表** - 水平分割
- **索引优化** - 复合索引
- **查询优化** - SQL调优

## 监控与运维

### 应用监控
- **Spring Boot Actuator** - 应用健康检查
- **Micrometer** - 指标收集
- **Logback** - 日志管理

### 系统监控
- **服务器监控** - CPU、内存、磁盘
- **数据库监控** - 连接数、慢查询
- **缓存监控** - Redis性能指标

## 安全架构

### 数据安全
- **数据加密** - AES加密
- **传输安全** - HTTPS/SSL
- **访问控制** - RBAC权限模型

### 接口安全
- **认证机制** - JWT Token
- **防刷机制** - 限流算法
- **参数校验** - JSR303验证

本技术栈架构支持高并发、高可用的业务需求，为八闽助业集市提供稳定可靠的技术保障。
