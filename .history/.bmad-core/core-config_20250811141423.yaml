# H5端专用配置
markdownExploder: true
prd:
  prdFile: docs/prd.md
  prdVersion: v4
  prdSharded: true
  prdShardedLocation: docs/prd
  epicFilePattern: epic-{n}*.md
architecture:
  architectureFile: docs/architecture.md
  architectureVersion: v4
  architectureSharded: true
  architectureShardedLocation: docs/architecture

# H5端专用文档加载
devLoadAlwaysFiles:
  # 后端API文档（开发时自动加载）
  - ../heartful-mall-backend/docs/architecture/api-specs.md
  - ../heartful-mall-backend/docs/architecture/endpoints.md
  - ../heartful-mall-backend/docs/architecture/business-logic.md
  
  # H5端专用文档
  - docs/architecture/api-integration.md
  - docs/architecture/wechat-integration.md
  - docs/architecture/h5-specific.md
  
  # 共享标准
  - ../docs/api-standards.md
  - ../docs/error-handling.md
  - ../docs/coding-standards.md

devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories
slashPrefix: BMad