# 八闽助业集市 Brownfield PRD

## 现有系统概述

八闽助业集市是一个多端电商平台，服务于福建省内的商家和消费者，助力乡村振兴和地方经济发展。

### 系统架构
- **后端API服务**: Spring Boot + jeecg-boot框架
- **管理后台**: Vue 2.6.10 + Ant Design Vue 1.7.2
- **C端用户小程序**: UniApp + Vue 3 + Composition API
- **商家端小程序**: UniApp + Vue 3 + Composition API
- **公众号H5端**: UniApp + Vue 3 + 微信JS SDK

### 技术栈
- **数据库**: MySQL 8.0 + Redis 6.0
- **后端框架**: Spring Boot 2.x + jeecg-boot 3.5.x + MyBatis Plus
- **前端框架**: Vue 2/3 + UniApp
- **状态管理**: Vuex (Vue2) / Pinia (Vue3)
- **网络请求**: axios (管理后台) / luch-request (小程序)

## 现有功能模块

### 用户管理系统
**现有功能**:
- 用户注册、登录、认证
- 用户信息管理和修改
- 用户余额管理和充值
- 用户等级和积分系统
- 用户收货地址管理

**数据表**:
- `user_info` - 用户基础信息
- `user_balance` - 用户余额
- `user_balance_log` - 余额变动日志
- `user_address` - 收货地址

**API接口**:
- `/front/user/register` - 用户注册
- `/front/user/login` - 用户登录
- `/after/user/info` - 获取用户信息
- `/after/user/update` - 更新用户信息
- `/after/balance/recharge` - 余额充值

### 商家管理系统
**现有功能**:
- 商家入驻和认证
- 店铺信息管理
- 商品管理（CRUD）
- 订单处理和发货
- 财务管理和提现

**数据表**:
- `store_info` - 店铺信息
- `store_goods` - 商品信息
- `store_order` - 订单信息
- `store_finance` - 财务记录

**API接口**:
- `/before/merchant/register` - 商家注册
- `/back/goods/manage` - 商品管理
- `/back/order/process` - 订单处理
- `/back/finance/withdraw` - 提现申请

### 订单管理系统
**现有功能**:
- 订单创建和支付
- 订单状态管理
- 物流跟踪
- 售后处理
- 退款管理

**数据表**:
- `order_info` - 订单基础信息
- `order_item` - 订单商品明细
- `order_payment` - 支付记录
- `order_logistics` - 物流信息

**API接口**:
- `/after/order/create` - 创建订单
- `/after/order/pay` - 订单支付
- `/after/order/list` - 订单列表
- `/after/order/detail` - 订单详情

### 商品管理系统
**现有功能**:
- 商品分类管理
- 商品信息展示
- 商品搜索和筛选
- 商品库存管理
- 商品评价系统

**数据表**:
- `goods_category` - 商品分类
- `goods_info` - 商品信息
- `goods_stock` - 库存管理
- `goods_review` - 商品评价

**API接口**:
- `/front/goods/list` - 商品列表
- `/front/goods/detail` - 商品详情
- `/front/goods/search` - 商品搜索
- `/front/category/list` - 分类列表

### 财务管理系统
**现有功能**:
- 资金流水记录
- 提现管理
- 财务统计报表
- 佣金计算
- 结算管理

**数据表**:
- `finance_log` - 资金流水
- `withdraw_record` - 提现记录
- `commission_record` - 佣金记录
- `settlement_record` - 结算记录

**API接口**:
- `/back/finance/overview` - 财务概览
- `/back/finance/withdraw` - 提现申请
- `/sys/finance/report` - 财务报表

### 系统管理
**现有功能**:
- 系统用户管理
- 角色权限管理
- 菜单管理
- 字典管理
- 系统配置

**数据表**:
- `sys_user` - 系统用户
- `sys_role` - 系统角色
- `sys_permission` - 系统权限
- `sys_dict` - 数据字典

**API接口**:
- `/sys/user/manage` - 用户管理
- `/sys/role/manage` - 角色管理
- `/sys/permission/manage` - 权限管理
- `/sys/dict/manage` - 字典管理

## 现有技术特性

### API设计规范
- **路径规范**: 
  - `/front/` - 公开接口（无需认证）
  - `/after/` - C端用户认证接口
  - `/before/` - 商家端公开接口
  - `/back/` - 商家端认证接口
  - `/sys/` - 系统管理接口

- **认证机制**:
  - C端用户: `userToken` + `Authorization` header
  - 商家端: `merchantToken` + `X-App-Type: merchant` header
  - 管理后台: `X-Access-Token` header

- **响应格式**:
```json
{
  "success": true,
  "code": 200,
  "message": "操作成功",
  "result": {},
  "timestamp": 1640995200000
}
```

### 数据库设计规范
- **基础字段**: 所有表包含 `id`, `create_time`, `update_time`, `create_by`, `update_by`, `del_flag`
- **金额字段**: 统一使用 `decimal(10,2)` 类型
- **命名规范**: 表名和字段名使用下划线命名法
- **索引策略**: 合理设置主键、唯一键和普通索引

### 前端设计规范
- **设计系统**: 
  - 品牌色: `#667eea`
  - 卡片圆角: `16rpx`
  - 组件圆角: `12rpx`
  - 基础间距: `16rpx`

- **组件规范**:
  - 管理后台: 使用 `a-form-model` 进行表单验证
  - 小程序: 使用 `uv-ui` 组件库
  - 状态管理: Vue2使用Vuex，Vue3使用Pinia

## 系统性能现状

### 用户规模
- 注册用户: 约10,000人
- 活跃商家: 约500家
- 日均订单: 约200单
- 月交易额: 约50万元

### 技术指标
- API平均响应时间: 200ms
- 数据库查询平均时间: 50ms
- 前端页面加载时间: 2-3秒
- 系统可用性: 99.5%

### 存储规模
- 数据库大小: 约2GB
- 图片存储: 约5GB
- 日志文件: 约1GB/月

## 计划增强功能

### 短期目标（1-3个月）
1. **用户体验优化**
   - 优化小程序加载速度
   - 改进搜索功能准确性
   - 增强商品推荐算法

2. **功能完善**
   - 增加用户评价系统
   - 完善售后服务流程
   - 优化支付流程

3. **性能优化**
   - 数据库查询优化
   - 缓存策略改进
   - 图片加载优化

### 中期目标（3-6个月）
1. **业务扩展**
   - 增加直播带货功能
   - 开发团购功能
   - 增加优惠券系统

2. **技术升级**
   - 微服务架构改造
   - 容器化部署
   - 监控系统完善

3. **数据分析**
   - 用户行为分析
   - 销售数据分析
   - 运营数据看板

### 长期目标（6-12个月）
1. **平台扩展**
   - 多地区支持
   - 多语言支持
   - 跨境电商功能

2. **智能化**
   - AI商品推荐
   - 智能客服
   - 自动化运营

3. **生态建设**
   - 开放API平台
   - 第三方服务集成
   - 合作伙伴生态

## 技术债务和改进计划

### 现有技术债务
1. **代码质量**
   - 部分代码缺乏注释
   - 单元测试覆盖率不足
   - 代码重复度较高

2. **架构问题**
   - 单体架构扩展性限制
   - 数据库性能瓶颈
   - 缓存策略不够完善

3. **安全问题**
   - 部分接口缺乏权限验证
   - 数据传输加密不完整
   - 日志记录不够详细

### 改进计划
1. **代码重构**
   - 提高代码注释覆盖率
   - 增加单元测试
   - 消除代码重复

2. **架构优化**
   - 逐步微服务化
   - 数据库读写分离
   - 完善缓存策略

3. **安全加固**
   - 完善权限验证
   - 加强数据加密
   - 改进日志系统

## 风险评估

### 技术风险
- **依赖风险**: 第三方服务依赖较多
- **性能风险**: 用户增长可能导致性能瓶颈
- **安全风险**: 电商平台面临的安全威胁

### 业务风险
- **竞争风险**: 市场竞争激烈
- **合规风险**: 电商相关法规变化
- **运营风险**: 商家和用户流失

### 缓解措施
- 建立完善的监控和告警系统
- 制定应急响应预案
- 定期进行安全评估
- 持续优化用户体验

## 成功指标

### 技术指标
- API响应时间 < 100ms
- 系统可用性 > 99.9%
- 页面加载时间 < 2秒
- 错误率 < 0.1%

### 业务指标
- 用户增长率 > 20%/月
- 商家满意度 > 90%
- 订单转化率 > 15%
- 月交易额增长 > 30%

### 用户体验指标
- 用户留存率 > 80%
- 应用评分 > 4.5分
- 客服响应时间 < 5分钟
- 投诉处理时间 < 24小时

通过持续的功能增强和技术优化，八闽助业集市将成为福建省内领先的电商平台，为商家和消费者提供更好的服务体验。
