# 后端项目专用BMAD配置
# 项目: heartful-mall-backend
# 技术栈: Spring Boot + jeecg-boot + MyBatis Plus
# 配置版本: 1.0.0
# 最后更新: 2025-01-12

markdownExploder: true
prd:
  prdFile: docs/prd.md
  prdVersion: v4
architecture:
  architectureFile: docs/architecture.md
  architectureVersion: v4
  architectureSharded: true
  architectureShardedLocation: docs/architecture

# 后端专用文档加载
devLoadAlwaysFiles:
  # 后端架构文档
  - docs/architecture/api-design.md
  - docs/architecture/database-design.md
  - docs/architecture/security-framework.md
  - docs/architecture/service-layer.md
  - docs/architecture/deployment-guide.md
  - docs/architecture/performance-optimization.md
  
  # 前端集成文档（开发时自动加载）
  - ../heartful-mall-web/docs/architecture/api-integration.md
  - ../heartful-mall-app/docs/architecture/api-integration.md
  - ../heartful-mall-merchants-pro/docs/architecture/api-integration.md
  - ../heartful-mall-h5/docs/architecture/api-integration.md
  
  # 工作区级共享标准
  - ../docs/开发规范/API路径规范.md
  - ../docs/开发规范/后端API开发规范.md
  - ../docs/开发规范/CRUD代码生成规范.md
  - ../docs/开发规范/接口对接验证流程.md
  - ../docs/开发规范/质量保证要求.md
  - ../.bmad-core/data/technical-preferences.md

devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories
slashPrefix: BMad

# 后端特定配置
backend:
  framework: "Spring Boot"
  version: "2.7.x"
  database: "MySQL 8.0"
  orm: "MyBatis Plus"
  security: "jeecg-boot"
  build: "Maven"
  
# API路径配置
apiPaths:
  public: "/front/"
  userAuth: "/after/"
  merchantPublic: "/before/"
  merchantAuth: "/back/"
  admin: "/sys/"
  
# 数据库配置
database:
  type: "MySQL"
  version: "8.0"
  charset: "utf8mb4"
  collation: "utf8mb4_unicode_ci"
  engine: "InnoDB"
  
# 认证配置
authentication:
  userToken: "JWT"
  merchantToken: "JWT"
  adminSession: "Session"
  
# 性能配置
performance:
  connectionPool: "HikariCP"
  cache: "Redis"
  queryOptimization: true
  indexOptimization: true
