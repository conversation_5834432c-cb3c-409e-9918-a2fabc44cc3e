# 后端项目专用BMAD配置
# 项目: heartful-mall-backend
# 技术栈: Spring Boot + jeecg-boot + MyBatis Plus
# 配置版本: 1.0.0
# 最后更新: 2025-01-12

markdownExploder: true
prd:
  prdFile: docs/prd.md
  prdVersion: v4
  prdSharded: true
  prdShardedLocation: docs/prd
  epicFilePattern: epic-{n}*.md
architecture:
  architectureFile: docs/architecture.md
  architectureVersion: v4
  architectureSharded: true
  architectureShardedLocation: docs/architecture

# 后端专用文档加载
devLoadAlwaysFiles:
  # API相关文档
  - docs/architecture/api-specs.md
  - docs/architecture/endpoints.md
  - docs/architecture/database-schema.md
  - docs/architecture/business-logic.md

  # 前端集成文档（开发时自动加载）
  - ../heartful-mall-web/docs/architecture/api-integration.md
  - ../heartful-mall-app/docs/architecture/api-integration.md
  - ../heartful-mall-merchants-pro/docs/architecture/api-integration.md
  - ../heartful-mall-h5/docs/architecture/api-integration.md

  # 工作区级共享标准
  - ../docs/api-standards.md
  - ../docs/error-handling.md
  - ../docs/security-standards.md
  - ../docs/开发规范/后端API开发规范.md
  - ../docs/开发规范/接口对接验证流程.md
  - ../docs/开发规范/质量保证要求.md
  - ../.bmad-core/data/technical-preferences.md

devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories
slashPrefix: BMad

# 后端特定配置
backend:
  framework: "Spring Boot + jeecg-boot"
  orm: "MyBatis Plus"
  database: "MySQL 8.0"
  cache: "Redis 6.0"
  build: "Maven"

# API路径配置
apiPaths:
  public: "/front/"
  userAuth: "/after/"
  merchantPublic: "/before/"
  merchantAuth: "/back/"
  admin: "/sys/"

# 数据库配置
database:
  amountType: "decimal"
  baseFields: ["id", "create_time", "update_time", "create_by", "update_by", "del_flag"]
  namingStrategy: "snake_case"

# 安全配置
security:
  authentication: "JWT"
  authorization: "RBAC"
  encryption: "AES"

# 性能优化配置
performance:
  cache:
    enabled: true
    ttl: 3600
    maxSize: 1000
    strategy: lru
  documentLoading:
    lazy: true
    compression: true
    batchSize: 5