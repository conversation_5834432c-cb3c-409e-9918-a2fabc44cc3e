# 管理后台专用BMAD配置
# 项目: heartful-mall-web
# 技术栈: Vue 2.6.10 + Ant Design Vue 1.7.2
# 配置版本: 1.0.0
# 最后更新: 2025-01-12

markdownExploder: true
prd:
  prdFile: docs/prd.md
  prdVersion: v4
architecture:
  architectureFile: docs/architecture.md
  architectureVersion: v4
  architectureSharded: true
  architectureShardedLocation: docs/architecture

# 管理后台专用文档加载
devLoadAlwaysFiles:
  # 后端API文档（开发时自动加载）
  - ../heartful-mall-backend/docs/architecture/api-design.md
  - ../heartful-mall-backend/docs/architecture/database-design.md
  - ../heartful-mall-backend/docs/architecture/security-framework.md
  - ../heartful-mall-backend/docs/architecture/service-layer.md

  # 管理后台专用文档
  - docs/architecture/api-integration.md
  - docs/architecture/component-standards.md
  - docs/architecture/state-management.md
  - docs/architecture/routing-standards.md
  - docs/architecture/ui-standards.md
  - docs/architecture/build-deployment.md

  # 工作区级共享标准
  - ../docs/开发规范/API路径规范.md
  - ../docs/开发规范/后端API开发规范.md
  - ../docs/开发规范/接口对接验证流程.md
  - ../docs/开发规范/质量保证要求.md
  - ../.bmad-core/data/technical-preferences.md

devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories
slashPrefix: BMad

# 管理后台特定配置
frontend:
  framework: "Vue 2.6.10"
  ui: "Ant Design Vue 1.7.2"
  state: "Vuex"
  http: "axios 0.18.0"
  build: "Vue CLI 4.x"

# API路径配置
apiPaths:
  admin: "/sys/"
  common: "/common/"

# 组件配置
components:
  form: "a-form-model"
  table: "a-table"
  list: "JeecgListMixin"
  dict: "j-dict-select-tag"

# 权限配置
permissions:
  directive: "v-has"
  annotation: "@perm"

# 构建配置
build:
  outputDir: "dist"
  publicPath: "/"
  sourceMap: false
  productionGzip: true