# H5端专用BMAD配置
# 项目: heartful-mall-h5
# 技术栈: UniApp + Vue 3 + 微信JS SDK
# 配置版本: 1.0.0
# 最后更新: 2025-01-12

markdownExploder: true
prd:
  prdFile: docs/prd.md
  prdVersion: v4
  prdSharded: true
  prdShardedLocation: docs/prd
  epicFilePattern: epic-{n}*.md
architecture:
  architectureFile: docs/architecture.md
  architectureVersion: v4
  architectureSharded: true
  architectureShardedLocation: docs/architecture

# H5端专用文档加载
devLoadAlwaysFiles:
  # 后端API文档（开发时自动加载）
  - ../heartful-mall-backend/docs/architecture/api-design.md
  - ../heartful-mall-backend/docs/architecture/database-design.md
  - ../heartful-mall-backend/docs/architecture/security-framework.md
  - ../heartful-mall-backend/docs/architecture/service-layer.md

  # H5端专用文档
  - docs/architecture/api-integration.md
  - docs/architecture/wechat-integration.md
  - docs/architecture/h5-specific.md
  - docs/architecture/component-standards.md
  - docs/architecture/state-management.md
  - docs/architecture/ui-standards.md

  # 工作区级共享标准
  - ../docs/开发规范/API路径规范.md
  - ../docs/开发规范/接口对接验证流程.md
  - ../docs/开发规范/质量保证要求.md
  - ../.bmad-core/data/technical-preferences.md

devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories
slashPrefix: BMad

# H5端特定配置
frontend:
  framework: "UniApp + Vue 3"
  platform: "H5 (微信公众号)"
  wechat: "微信JS SDK 1.6.0"
  enterprise: "企业微信SDK"
  http: "luch-request"
  state: "Pinia"
  build: "HBuilderX + Vite"

# API路径配置
apiPaths:
  public: "/front/"
  userAuth: "/after/"

# 微信集成配置
wechat:
  jsSDK: "1.6.0"
  features: ["login", "share", "pay", "scan"]
  enterprise: true

# 设计系统配置
design:
  primaryColor: "#667eea"
  cardRadius: "16rpx"
  componentRadius: "12rpx"
  spacing: "16rpx"

# 认证配置
authentication:
  tokenType: "userToken"
  header: "Authorization"
  storage: "localStorage"

# 性能优化配置
performance:
  cache:
    enabled: true
    ttl: 3600
    maxSize: 1000
    strategy: lru
  documentLoading:
    lazy: true
    compression: true
    batchSize: 5
  imageLoading:
    lazyLoad: true
    placeholder: true
    webp: true