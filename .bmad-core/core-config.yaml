# 商家端小程序专用BMAD配置
# 项目: heartful-mall-merchants-pro
# 技术栈: UniApp + Vue 3 + Composition API + Pinia
# 配置版本: 1.0.0
# 最后更新: 2025-01-12

markdownExploder: true
prd:
  prdFile: docs/prd.md
  prdVersion: v4
architecture:
  architectureFile: docs/architecture.md
  architectureVersion: v4
  architectureSharded: true
  architectureShardedLocation: docs/architecture

# 商家端小程序专用文档加载
devLoadAlwaysFiles:
  # 后端API文档（开发时自动加载）
  - ../heartful-mall-backend/docs/architecture/api-design.md
  - ../heartful-mall-backend/docs/architecture/database-design.md
  - ../heartful-mall-backend/docs/architecture/security-framework.md
  - ../heartful-mall-backend/docs/architecture/service-layer.md

  # 商家端小程序专用文档
  - docs/architecture/api-integration.md
  - docs/architecture/component-standards.md
  - docs/architecture/state-management.md
  - docs/architecture/ui-standards.md
  - docs/architecture/build-deployment.md

  # 工作区级共享标准
  - ../docs/开发规范/API路径规范.md
  - ../docs/开发规范/接口对接验证流程.md
  - ../docs/开发规范/质量保证要求.md
  - ../.bmad-core/data/technical-preferences.md

devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories
slashPrefix: BMad

# 商家端小程序特定配置
frontend:
  framework: "UniApp + Vue 3"
  ui: "uv-ui"
  state: "Pinia"
  http: "luch-request"
  build: "HBuilderX"

# API路径配置
apiPaths:
  merchantPublic: "/before/"
  merchantAuth: "/back/"

# 设计系统配置
design:
  primaryColor: "#667eea"
  cardRadius: "16rpx"
  componentRadius: "12rpx"
  spacing: "16rpx"

# 认证配置
authentication:
  tokenType: "merchantToken"
  header: "Authorization"
  appTypeHeader: "X-App-Type: merchant"
  storage: "uni.getStorageSync"

# 商家端特色功能
features:
  productManagement: true
  orderProcessing: true
  dataStatistics: true
  customerService: true
  financeManagement: true