# 八闽助业集市技术偏好配置

## 项目级技术偏好

### 架构模式
- 后端：Spring Boot微服务架构，RESTful API设计，jeecg-boot框架
- 管理后台：Vue 2.6.10 + Ant Design Vue 1.7.2传统组件化开发
- UniApp项目：Vue 3 + Composition API + Pinia状态管理
- 数据库：MySQL关系型数据库 + Redis缓存层

### 技术栈规范
- 后端：Spring Boot 2.x + jeecg-boot + MyBatis Plus
- 管理后台：Vue 2.6.10 + Ant Design Vue 1.7.2 + axios 0.18.0
- UniApp：Vue 3 + Composition API + Pinia + luch-request
- 数据库：MySQL 8.0 + Redis 6.0
- 构建工具：Maven (后端) + npm/HBuilderX (前端)

### API路径规范
- `/front/` - 公开接口（无需认证）
- `/after/` - C端用户认证接口
- `/before/` - 商家端公开接口
- `/back/` - 商家端认证接口
- `/sys/` - 系统管理接口

### 认证机制
- C端用户：userToken + Authorization header
- 商家端：merchantToken + X-App-Type: merchant header
- 管理后台：传统session认证

### 数据库规范
- 金额字段：必须使用decimal类型
- 所有表：必须包含基础字段（id, create_time, update_time, create_by, update_by）
- 枚举字段：必须配置对应的字典
- 表名：使用下划线命名法
- 字段名：使用下划线命名法

### 代码规范
- Java：遵循阿里巴巴Java开发规范
- Vue2：使用Options API，a-form-model表单验证
- Vue3：使用Composition API，setup语法糖
- 命名：中文业务场景使用中文命名，技术术语使用英文

### 错误处理
- 统一错误码格式
- 统一响应格式
- 结构化日志记录
- 请求追踪ID

## 前端项目特定偏好

### 管理后台 (heartful-mall-web)
- 强制使用a-form-model进行表单验证
- 使用JeecgListMixin进行列表管理
- axios配置180秒超时
- 字典组件集成
- 权限控制：@perm注解

### C端用户小程序 (heartful-mall-app)
- 使用luch-request进行网络请求
- 品牌色：#667eea
- 现代卡片式设计，16rpx圆角
- API路径：/front/（公开）、/after/（认证）
- 认证：userToken

### 商家端小程序 (heartful-mall-merchants-pro)
- API路径：/before/（公开）、/back/（认证）
- 认证：merchantToken + X-App-Type: merchant
- 特色功能：商品管理、订单处理、数据统计
- 工作流程：商家专用操作流程

### H5端 (heartful-mall-h5)
- 微信JS SDK集成
- 企业微信对接
- 微信环境检测
- 分享功能
- 微信支付集成

## 后端项目特定偏好

### 代码结构
- jeecg-boot模块化结构
- Controller-Service-Mapper分层
- 统一异常处理
- 统一响应格式

### 数据库操作
- MyBatis Plus代码生成
- 事务管理
- 查询优化
- 连接池配置

### 安全规范
- JWT认证
- 权限注解
- 数据权限控制
- 接口防刷

## 跨项目集成偏好

### 数据一致性
- API响应格式统一
- 数据类型匹配
- 错误处理一致
- 日志格式统一

### 开发流程
- 跨端Story开发
- API接口验证
- 数据流验证
- 认证机制验证

### 部署规范
- Docker容器化
- 环境配置分离
- 数据库迁移脚本
- 缓存策略
