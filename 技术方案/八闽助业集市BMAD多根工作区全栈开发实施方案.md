# 八闽助业集市 BMAD Brownfield 实施方案

## 项目概述

八闽助业集市是一个**遗留项目（Brownfield）**，包含5个子项目的多根工作区架构。本方案严格遵循BMAD官方标准和Brownfield最佳实践。

### 项目结构
```
heartful-mall/                          # 工作区根目录
├── heartful-mall-backend/              # 后端API服务 (Spring Boot + jeecg-boot)
├── heartful-mall-web/                  # 管理后台 (Vue2 + Ant Design Vue)
├── heartful-mall-app/                  # C端用户小程序 (UniApp + Vue3)
├── heartful-mall-merchants-pro/        # 商家端小程序 (UniApp + Vue3)
├── heartful-mall-h5/                   # 公众号H5端 (UniApp + Vue3)
├── docs/                               # 共享文档中心
└── .bmad-core/                         # 工作区级BMAD配置
```

### 技术栈概览
- **后端**: Spring Boot + jeecg-boot + MyBatis Plus + MySQL + Redis
- **管理后台**: Vue 2.6.10 + Ant Design Vue 1.7.2
- **小程序**: UniApp + Vue 3 + Composition API + Pinia
- **数据库**: MySQL 8.0 + Redis 6.0
- **认证**: 多端差异化认证机制

## 第一阶段：Brownfield项目初始化

### 1.1 项目概览生成

#### 1.1.1 生成项目概览
```bash
# 在工作区根目录执行
cd /Users/<USER>/Dev/SideWork/heartful-mall

# 生成项目完整概览（Brownfield必需步骤）
npx bmad-method flatten

# 这将创建项目的完整快照，帮助AI理解现有系统结构
```

#### 1.1.2 BMAD方法安装
```bash
# 工作区级安装
npx bmad-method install

# 各子项目安装
cd heartful-mall-backend && npx bmad-method install && cd ..
cd heartful-mall-web && npx bmad-method install && cd ..
cd heartful-mall-app && npx bmad-method install && cd ..
cd heartful-mall-merchants-pro && npx bmad-method install && cd ..
cd heartful-mall-h5 && npx bmad-method install && cd ..

# 验证安装
find . -name ".bmad-core" -type d
# 应显示6个.bmad-core目录
```

### 1.2 Brownfield标准配置

#### 1.2.1 工作区级标准配置 (.bmad-core/core-config.yaml)
```yaml
# 八闽助业集市工作区级BMAD配置
# 遵循BMAD官方标准 - Brownfield项目配置
markdownExploder: true

# Brownfield项目PRD配置
prd:
  prdFile: docs/brownfield-prd.md
  prdVersion: v4
  prdSharded: true
  prdShardedLocation: docs/prd
  epicFilePattern: epic-{n}*.md

# Brownfield项目架构配置
architecture:
  architectureFile: docs/brownfield-architecture.md
  architectureVersion: v4
  architectureSharded: true
  architectureShardedLocation: docs/architecture

# 文档自动加载（仅引用存在的文档）
devLoadAlwaysFiles:
  # 工作区级架构文档
  - docs/architecture/coding-standards.md
  - docs/architecture/tech-stack.md
  - docs/architecture/source-tree.md

  # 技术偏好配置
  - .bmad-core/data/technical-preferences.md

# BMAD标准配置
devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories
slashPrefix: BMad
```

#### 1.2.2 技术偏好配置 (.bmad-core/data/technical-preferences.md)
```markdown
# 八闽助业集市技术偏好配置

## 遗留系统技术栈

### 后端技术栈
- **框架**: Spring Boot 2.x + jeecg-boot 3.5.x
- **ORM**: MyBatis Plus 3.5.x
- **数据库**: MySQL 8.0 + Redis 6.0
- **构建**: Maven 3.8.x + JDK 1.8
- **架构**: RESTful API + 分层架构

### 前端技术栈
- **管理后台**: Vue 2.6.10 + Ant Design Vue 1.7.2
- **小程序**: UniApp + Vue 3 + Composition API
- **状态管理**: Vuex (Vue2) / Pinia (Vue3)
- **网络请求**: axios (管理后台) / luch-request (小程序)

## 核心业务规范

### API路径规范
- `/front/` - 公开接口（无需认证）
- `/after/` - C端用户认证接口
- `/before/` - 商家端公开接口
- `/back/` - 商家端认证接口
- `/sys/` - 系统管理接口

### 认证机制
- **C端用户**: userToken + Authorization header
- **商家端**: merchantToken + X-App-Type header
- **管理后台**: X-Access-Token header

### 数据库规范
- **金额字段**: 必须使用decimal类型
- **基础字段**: id, create_time, update_time, create_by, update_by, del_flag
- **命名规范**: 表名和字段名使用下划线命名法
- **字典配置**: 枚举字段必须配置对应字典

### 代码规范
- **Java**: 阿里巴巴Java开发规范
- **Vue2**: Options API + a-form-model表单验证
- **Vue3**: Composition API + setup语法糖
- **命名**: 业务术语中文，技术术语英文

## 项目特定配置

### 设计系统
- **品牌色**: #667eea
- **卡片圆角**: 16rpx
- **组件圆角**: 12rpx
- **基础间距**: 16rpx

### 错误处理
- 统一错误码和响应格式
- 结构化日志记录
- 请求追踪ID

### 性能优化
- 数据库查询优化
- Redis缓存策略
- 前端资源懒加载
- 图片压缩和CDN
```

### 1.3 子项目标准配置

#### 1.3.1 后端项目配置 (heartful-mall-backend/.bmad-core/core-config.yaml)
```yaml
# 后端项目BMAD标准配置
markdownExploder: true

# Brownfield项目配置
prd:
  prdFile: docs/brownfield-prd.md
  prdVersion: v4
  prdSharded: true
  prdShardedLocation: docs/prd

architecture:
  architectureFile: docs/brownfield-architecture.md
  architectureVersion: v4
  architectureSharded: true
  architectureShardedLocation: docs/architecture

# 后端专用文档加载（仅引用存在的文档）
devLoadAlwaysFiles:
  # 后端架构文档
  - docs/architecture/api-design.md
  - docs/architecture/database-design.md
  - docs/architecture/security-framework.md
  - docs/architecture/service-layer.md

  # 工作区级共享文档
  - ../docs/architecture/coding-standards.md
  - ../docs/architecture/tech-stack.md
  - ../.bmad-core/data/technical-preferences.md

devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories
slashPrefix: BMad
```

#### 1.3.2 前端项目配置模板
所有前端项目（web、app、merchants-pro、h5）使用相同的标准配置结构：

```yaml
# 前端项目BMAD标准配置
markdownExploder: true

# Brownfield项目配置
prd:
  prdFile: docs/brownfield-prd.md
  prdVersion: v4
  prdSharded: true
  prdShardedLocation: docs/prd

architecture:
  architectureFile: docs/brownfield-architecture.md
  architectureVersion: v4
  architectureSharded: true
  architectureShardedLocation: docs/architecture

# 前端专用文档加载（仅引用存在的文档）
devLoadAlwaysFiles:
  # 项目特定架构文档
  - docs/architecture/api-integration.md
  - docs/architecture/component-standards.md

  # 后端API文档（开发时自动加载）
  - ../heartful-mall-backend/docs/architecture/api-design.md

  # 工作区级共享文档
  - ../docs/architecture/coding-standards.md
  - ../docs/architecture/tech-stack.md
  - ../.bmad-core/data/technical-preferences.md

devDebugLog: .ai/debug-log.md
devStoryLocation: docs/stories
slashPrefix: BMad
```

## 第二阶段：Brownfield文档创建

### 2.1 创建标准Brownfield文档

#### 2.1.1 记录现有系统
使用AI代理的`document-project`命令记录现有系统结构和功能。

#### 2.1.2 创建Brownfield PRD和架构文档
基于现有系统分析，创建标准的brownfield-prd.md和brownfield-architecture.md文档。

## 第三阶段：BMAD工作流执行

### 3.1 Brownfield开发工作流

#### 3.1.1 系统记录阶段
```bash
# 使用AI代理记录现有系统
npx bmad-method document-project

# 这将分析现有代码结构，生成系统概览
```

#### 3.1.2 增强计划制定
基于现有系统分析，制定功能增强和技术改进计划。

#### 3.1.3 分片文档管理
使用BMAD的分片功能管理大型文档：
- PRD分片：docs/prd/epic-*.md
- 架构分片：docs/architecture/*.md

### 3.2 开发代理协作

#### 3.2.1 Story驱动开发
- 创建跨端Story文档
- 使用AI代理协作开发
- 自动加载相关项目文档

#### 3.2.2 质量保证
- 代码审查
- 接口验证
- 跨项目一致性检查

## 第四阶段：验证和优化

### 4.1 配置验证
```bash
# 验证BMAD配置
npx bmad-method validate

# 检查文档引用
npx bmad-method check-references
```

### 4.2 工作流优化
根据实际使用情况，持续优化BMAD配置和工作流程。

## 总结

本实施方案严格遵循BMAD官方标准，特别是Brownfield项目的最佳实践：

1. **标准化配置**：使用BMAD标准配置格式，避免过度定制
2. **Brownfield规范**：使用brownfield-prd.md和brownfield-architecture.md
3. **简洁原则**：配置文件简洁，只引用存在的文档
4. **渐进式增强**：基于现有系统逐步改进

通过这种方式，八闽助业集市项目将能够充分利用BMAD方法的优势，实现高效的AI辅助开发。