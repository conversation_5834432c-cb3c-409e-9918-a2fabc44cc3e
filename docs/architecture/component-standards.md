# 八闽助业集市C端小程序组件开发标准

## 🏗️ UniApp + Vue3组件架构

### 技术栈组合
```
┌─────────────────────────────────────────────────────────────────┐
│                    C端小程序技术栈                               │
├─────────────────────────────────────────────────────────────────┤
│  UniApp          → 跨平台开发框架                               │
│  Vue3            → 渐进式JavaScript框架                         │
│  Composition API → Vue3组合式API                               │
│  uv-ui           → UniApp生态UI组件库                          │
│  Pinia           → Vue3状态管理库                              │
│  luch-request    → 网络请求库                                  │
└─────────────────────────────────────────────────────────────────┘
```

### 组件设计原则
- **现代化**: 使用Vue3 Composition API
- **响应式**: 适配不同屏幕尺寸
- **性能优化**: 组件懒加载和缓存
- **用户体验**: 流畅的交互和反馈
- **一致性**: 统一的设计语言

## 🎨 现代卡片式设计规范

### 设计系统
```less
// 设计令牌
:root {
  // 品牌色
  --primary-color: #667eea;
  --primary-light: #8fa4f3;
  --primary-dark: #4c63d2;
  
  // 功能色
  --success-color: #07c160;
  --warning-color: #ff976a;
  --error-color: #ee0a24;
  --info-color: #1989fa;
  
  // 中性色
  --text-primary: #323233;
  --text-secondary: #646566;
  --text-disabled: #c8c9cc;
  --background: #f7f8fa;
  --white: #ffffff;
  
  // 圆角
  --border-radius-card: 16px;
  --border-radius-component: 12px;
  --border-radius-small: 8px;
  
  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  // 阴影
  --shadow-light: 0 2px 12px rgba(100, 101, 102, 0.08);
  --shadow-medium: 0 4px 20px rgba(100, 101, 102, 0.12);
  --shadow-heavy: 0 8px 32px rgba(100, 101, 102, 0.16);
}
```

### 卡片组件规范
```vue
<template>
  <view class="card-container" :class="cardClass">
    <view v-if="title || $slots.header" class="card-header">
      <slot name="header">
        <text class="card-title">{{ title }}</text>
        <text v-if="subtitle" class="card-subtitle">{{ subtitle }}</text>
      </slot>
    </view>
    
    <view class="card-body">
      <slot></slot>
    </view>
    
    <view v-if="$slots.footer" class="card-footer">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: String,
  subtitle: String,
  shadow: {
    type: String,
    default: 'light',
    validator: value => ['none', 'light', 'medium', 'heavy'].includes(value)
  },
  padding: {
    type: String,
    default: 'md',
    validator: value => ['none', 'sm', 'md', 'lg'].includes(value)
  },
  radius: {
    type: String,
    default: 'card',
    validator: value => ['none', 'small', 'component', 'card'].includes(value)
  }
})

const cardClass = computed(() => ({
  [`card-shadow-${props.shadow}`]: props.shadow !== 'none',
  [`card-padding-${props.padding}`]: props.padding !== 'none',
  [`card-radius-${props.radius}`]: props.radius !== 'none'
}))
</script>

<style lang="scss" scoped>
.card-container {
  background: var(--white);
  overflow: hidden;
  
  &.card-shadow-light {
    box-shadow: var(--shadow-light);
  }
  
  &.card-shadow-medium {
    box-shadow: var(--shadow-medium);
  }
  
  &.card-shadow-heavy {
    box-shadow: var(--shadow-heavy);
  }
  
  &.card-radius-small {
    border-radius: var(--border-radius-small);
  }
  
  &.card-radius-component {
    border-radius: var(--border-radius-component);
  }
  
  &.card-radius-card {
    border-radius: var(--border-radius-card);
  }
  
  &.card-padding-sm {
    .card-body {
      padding: var(--spacing-sm);
    }
  }
  
  &.card-padding-md {
    .card-body {
      padding: var(--spacing-md);
    }
  }
  
  &.card-padding-lg {
    .card-body {
      padding: var(--spacing-lg);
    }
  }
}

.card-header {
  padding: var(--spacing-md) var(--spacing-md) 0;
  
  .card-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    line-height: 1.4;
  }
  
  .card-subtitle {
    font-size: 14px;
    color: var(--text-secondary);
    margin-top: var(--spacing-xs);
  }
}

.card-footer {
  padding: 0 var(--spacing-md) var(--spacing-md);
  border-top: 1px solid #f0f0f0;
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
}
</style>
```

## 📱 Vue3 Composition API规范

### 组合式函数使用
```vue
<template>
  <view class="product-list">
    <uv-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <uv-list @scrolltolower="onLoadMore">
        <view v-for="item in productList" :key="item.id" class="product-item">
          <product-card 
            :product="item" 
            @click="handleProductClick(item)"
            @favorite="handleFavorite(item)"
          />
        </view>
        
        <uv-load-more 
          :status="loadStatus" 
          :loading-text="loadingText"
          :no-more-text="noMoreText"
        />
      </uv-list>
    </uv-pull-refresh>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useProductList } from '@/composables/useProductList'
import { useUserStore } from '@/stores/user'
import ProductCard from '@/components/ProductCard.vue'

// 组合式函数
const {
  productList,
  loading,
  refreshing,
  loadStatus,
  loadingText,
  noMoreText,
  loadProductList,
  refreshProductList,
  loadMoreProducts
} = useProductList()

const userStore = useUserStore()

// 事件处理
const onRefresh = async () => {
  await refreshProductList()
}

const onLoadMore = async () => {
  await loadMoreProducts()
}

const handleProductClick = (product) => {
  uni.navigateTo({
    url: `/pages/product/detail?id=${product.id}`
  })
}

const handleFavorite = async (product) => {
  if (!userStore.isLogin) {
    uni.navigateTo({
      url: '/pages/user/login'
    })
    return
  }
  
  try {
    await userStore.toggleFavorite(product.id)
    uni.showToast({
      title: product.isFavorite ? '已取消收藏' : '收藏成功',
      icon: 'success'
    })
  } catch (error) {
    uni.showToast({
      title: '操作失败',
      icon: 'error'
    })
  }
}

// 生命周期
onMounted(() => {
  loadProductList()
})
</script>
```

### 自定义组合式函数
```javascript
// composables/useProductList.js
import { ref, computed } from 'vue'
import { productApi } from '@/api/product'

export function useProductList(categoryId = null) {
  const productList = ref([])
  const loading = ref(false)
  const refreshing = ref(false)
  const hasMore = ref(true)
  const page = ref(1)
  const pageSize = 20
  
  // 计算属性
  const loadStatus = computed(() => {
    if (loading.value) return 'loading'
    if (!hasMore.value) return 'nomore'
    return 'loadmore'
  })
  
  const loadingText = computed(() => '加载中...')
  const noMoreText = computed(() => '没有更多了')
  
  // 加载商品列表
  const loadProductList = async (reset = false) => {
    if (loading.value) return
    
    try {
      loading.value = true
      
      if (reset) {
        page.value = 1
        hasMore.value = true
      }
      
      const response = await productApi.getProductList({
        page: page.value,
        pageSize,
        categoryId: categoryId
      })
      
      if (response.success) {
        const newList = response.result.records || []
        
        if (reset) {
          productList.value = newList
        } else {
          productList.value.push(...newList)
        }
        
        hasMore.value = newList.length === pageSize
        if (hasMore.value) {
          page.value++
        }
      }
    } catch (error) {
      console.error('加载商品列表失败:', error)
      uni.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      loading.value = false
      refreshing.value = false
    }
  }
  
  // 刷新列表
  const refreshProductList = async () => {
    refreshing.value = true
    await loadProductList(true)
  }
  
  // 加载更多
  const loadMoreProducts = async () => {
    if (!hasMore.value || loading.value) return
    await loadProductList(false)
  }
  
  return {
    productList,
    loading,
    refreshing,
    hasMore,
    loadStatus,
    loadingText,
    noMoreText,
    loadProductList,
    refreshProductList,
    loadMoreProducts
  }
}
```

## 🎛️ uv-ui组件使用规范

### 基础组件使用
```vue
<template>
  <view class="form-container">
    <!-- 输入框 -->
    <uv-form ref="formRef" :model="form" :rules="rules">
      <uv-form-item label="手机号" prop="phone">
        <uv-input 
          v-model="form.phone" 
          placeholder="请输入手机号"
          type="number"
          maxlength="11"
          :clearable="true"
        />
      </uv-form-item>
      
      <!-- 验证码 -->
      <uv-form-item label="验证码" prop="code">
        <view class="code-input-wrapper">
          <uv-input 
            v-model="form.code" 
            placeholder="请输入验证码"
            type="number"
            maxlength="6"
            class="code-input"
          />
          <uv-button 
            :disabled="codeDisabled" 
            :loading="codeSending"
            @click="sendCode"
            class="code-button"
            size="small"
            type="primary"
            :plain="true"
          >
            {{ codeText }}
          </uv-button>
        </view>
      </uv-form-item>
      
      <!-- 选择器 -->
      <uv-form-item label="性别" prop="gender">
        <uv-radio-group v-model="form.gender" direction="row">
          <uv-radio name="1" label="男" />
          <uv-radio name="2" label="女" />
        </uv-radio-group>
      </uv-form-item>
      
      <!-- 日期选择 -->
      <uv-form-item label="生日" prop="birthday">
        <uv-datetime-picker 
          v-model="form.birthday"
          mode="date"
          :show="showDatePicker"
          @confirm="onDateConfirm"
          @cancel="showDatePicker = false"
        />
        <uv-input 
          v-model="birthdayText" 
          placeholder="请选择生日"
          readonly
          @click="showDatePicker = true"
        />
      </uv-form-item>
      
      <!-- 提交按钮 -->
      <view class="form-actions">
        <uv-button 
          type="primary" 
          :loading="submitting"
          @click="handleSubmit"
          customStyle="width: 100%; margin-top: 32px;"
        >
          提交
        </uv-button>
      </view>
    </uv-form>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useCountdown } from '@/composables/useCountdown'

const formRef = ref()
const showDatePicker = ref(false)
const submitting = ref(false)

// 表单数据
const form = ref({
  phone: '',
  code: '',
  gender: '',
  birthday: ''
})

// 表单验证规则
const rules = {
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 6, message: '验证码长度为6位', trigger: 'blur' }
  ],
  gender: [
    { required: true, message: '请选择性别', trigger: 'change' }
  ]
}

// 验证码倒计时
const { count, counting, start: startCountdown } = useCountdown(60)

const codeDisabled = computed(() => counting.value || !form.value.phone)
const codeSending = ref(false)
const codeText = computed(() => counting.value ? `${count.value}s` : '发送验证码')

// 生日显示文本
const birthdayText = computed(() => {
  return form.value.birthday ? form.value.birthday.split(' ')[0] : ''
})

// 发送验证码
const sendCode = async () => {
  if (!form.value.phone) {
    uni.showToast({ title: '请先输入手机号', icon: 'error' })
    return
  }
  
  try {
    codeSending.value = true
    // 调用发送验证码API
    await sendVerificationCode(form.value.phone)
    startCountdown()
    uni.showToast({ title: '验证码已发送', icon: 'success' })
  } catch (error) {
    uni.showToast({ title: '发送失败', icon: 'error' })
  } finally {
    codeSending.value = false
  }
}

// 日期确认
const onDateConfirm = (value) => {
  form.value.birthday = value
  showDatePicker.value = false
}

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value.validate()
    if (!valid) return
    
    submitting.value = true
    // 提交表单数据
    await submitForm(form.value)
    
    uni.showToast({ title: '提交成功', icon: 'success' })
    uni.navigateBack()
  } catch (error) {
    uni.showToast({ title: '提交失败', icon: 'error' })
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.form-container {
  padding: var(--spacing-md);
}

.code-input-wrapper {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  
  .code-input {
    flex: 1;
  }
  
  .code-button {
    flex-shrink: 0;
    width: 100px;
  }
}

.form-actions {
  margin-top: var(--spacing-xl);
}
</style>
```

## 🌐 网络请求规范

### luch-request配置
```javascript
// utils/request.js
import Request from 'luch-request'
import { useUserStore } from '@/stores/user'

// 创建请求实例
const http = new Request({
  baseURL: process.env.VUE_APP_API_BASE_URL,
  timeout: 30000,
  header: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
http.interceptors.request.use(
  config => {
    // 显示加载提示
    if (config.showLoading !== false) {
      uni.showLoading({
        title: config.loadingText || '加载中...',
        mask: true
      })
    }
    
    // 添加token
    const userStore = useUserStore()
    if (userStore.token) {
      config.header['X-Access-Token'] = userStore.token
    }
    
    // 添加时间戳防止缓存
    if (config.method === 'GET') {
      config.params = {
        ...config.params,
        _t: Date.now()
      }
    }
    
    return config
  },
  error => {
    uni.hideLoading()
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  response => {
    uni.hideLoading()
    
    const { data } = response
    
    // 请求成功
    if (data.success) {
      return data
    }
    
    // 业务错误处理
    if (data.code === 401) {
      // token过期，跳转登录
      const userStore = useUserStore()
      userStore.logout()
      uni.navigateTo({
        url: '/pages/user/login'
      })
      return Promise.reject(new Error('登录已过期'))
    }
    
    // 其他业务错误
    const errorMessage = data.message || '请求失败'
    uni.showToast({
      title: errorMessage,
      icon: 'error'
    })
    
    return Promise.reject(new Error(errorMessage))
  },
  error => {
    uni.hideLoading()
    
    let errorMessage = '网络错误'
    
    if (error.response) {
      const { status } = error.response
      switch (status) {
        case 400:
          errorMessage = '请求参数错误'
          break
        case 401:
          errorMessage = '未授权访问'
          break
        case 403:
          errorMessage = '权限不足'
          break
        case 404:
          errorMessage = '请求资源不存在'
          break
        case 500:
          errorMessage = '服务器内部错误'
          break
        default:
          errorMessage = `请求失败 ${status}`
      }
    } else if (error.message.includes('timeout')) {
      errorMessage = '请求超时'
    } else if (error.message.includes('Network Error')) {
      errorMessage = '网络连接异常'
    }
    
    uni.showToast({
      title: errorMessage,
      icon: 'error'
    })
    
    return Promise.reject(error)
  }
)

export default http
```

## 📋 组件开发最佳实践

### 1. 组件命名规范
- 使用PascalCase命名组件
- 组件文件名与组件名保持一致
- 使用描述性的组件名称

### 2. Props设计原则
- 使用TypeScript类型定义
- 提供合理的默认值
- 添加属性验证

### 3. 事件处理规范
- 使用emit定义组件事件
- 事件名使用kebab-case
- 提供清晰的事件参数

### 4. 样式规范
- 使用CSS变量定义设计令牌
- 遵循BEM命名规范
- 支持主题定制

### 5. 性能优化
- 合理使用v-show和v-if
- 避免在模板中使用复杂计算
- 使用组件懒加载

---

**文档说明**: 本文档为八闽助业集市C端小程序组件开发标准，基于UniApp + Vue3 + uv-ui技术栈，所有组件开发必须严格遵循此规范。
