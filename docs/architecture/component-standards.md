# 八闽助业集市商家端小程序组件开发标准

## 🏗️ 商家端技术架构

### 技术栈组合
```
┌─────────────────────────────────────────────────────────────────┐
│                    商家端小程序技术栈                            │
├─────────────────────────────────────────────────────────────────┤
│  UniApp          → 跨平台开发框架                               │
│  Vue3            → 渐进式JavaScript框架                         │
│  Composition API → Vue3组合式API                               │
│  Pinia           → Vue3状态管理库                              │
│  luch-request    → 网络请求库                                  │
│  现代卡片式设计   → 与C端保持一致的设计语言                      │
└─────────────────────────────────────────────────────────────────┘
```

### 商家端特色功能
- **商家认证**: 基于/before/和/back/路径的双重认证体系
- **数据统计**: 丰富的图表和数据可视化组件
- **商品管理**: 完整的商品CRUD操作界面
- **订单处理**: 订单状态管理和批量操作
- **营销工具**: 优惠券、活动管理等营销功能

## 🎨 商家端设计系统

### 设计令牌（继承C端）
```scss
// design-tokens.scss
:root {
  /* ========== 商家端专用色彩 ========== */
  --merchant-primary: #667eea;
  --merchant-success: #07c160;
  --merchant-warning: #ff976a;
  --merchant-error: #ee0a24;
  --merchant-info: #1989fa;
  
  /* ========== 商家端功能色彩 ========== */
  --revenue-color: #52c41a;
  --order-color: #1890ff;
  --product-color: #722ed1;
  --customer-color: #eb2f96;
  
  /* ========== 状态色彩 ========== */
  --status-pending: #faad14;
  --status-processing: #1890ff;
  --status-completed: #52c41a;
  --status-cancelled: #f5222d;
  --status-refund: #fa8c16;
  
  /* ========== 继承C端设计令牌 ========== */
  --primary-color: #667eea;
  --border-radius-card: 16px;
  --border-radius-component: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.06);
}
```

### 商家端专用组件
```vue
<template>
  <view class="merchant-dashboard">
    <!-- 数据概览卡片 -->
    <view class="stats-grid">
      <stats-card
        v-for="stat in statsData"
        :key="stat.key"
        :title="stat.title"
        :value="stat.value"
        :trend="stat.trend"
        :color="stat.color"
        @click="handleStatClick(stat)"
      />
    </view>
    
    <!-- 图表组件 -->
    <view class="chart-section">
      <chart-card title="销售趋势">
        <line-chart :data="salesData" :options="chartOptions" />
      </chart-card>
      
      <chart-card title="商品分析">
        <pie-chart :data="productData" :options="pieOptions" />
      </chart-card>
    </view>
    
    <!-- 快速操作 -->
    <view class="quick-actions">
      <action-button
        v-for="action in quickActions"
        :key="action.key"
        :icon="action.icon"
        :title="action.title"
        :color="action.color"
        @click="handleQuickAction(action)"
      />
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useMerchantStore } from '@/stores/merchant'
import StatsCard from '@/components/StatsCard.vue'
import ChartCard from '@/components/ChartCard.vue'
import LineChart from '@/components/charts/LineChart.vue'
import PieChart from '@/components/charts/PieChart.vue'
import ActionButton from '@/components/ActionButton.vue'

const merchantStore = useMerchantStore()

// 统计数据
const statsData = ref([
  {
    key: 'revenue',
    title: '今日营收',
    value: '¥12,345',
    trend: '+12.5%',
    color: 'var(--revenue-color)'
  },
  {
    key: 'orders',
    title: '今日订单',
    value: '156',
    trend: '+8.3%',
    color: 'var(--order-color)'
  },
  {
    key: 'products',
    title: '商品总数',
    value: '1,234',
    trend: '+2.1%',
    color: 'var(--product-color)'
  },
  {
    key: 'customers',
    title: '客户总数',
    value: '5,678',
    trend: '+15.7%',
    color: 'var(--customer-color)'
  }
])

// 快速操作
const quickActions = ref([
  {
    key: 'add-product',
    icon: 'plus-circle',
    title: '添加商品',
    color: 'var(--primary-color)'
  },
  {
    key: 'manage-orders',
    icon: 'file-text',
    title: '订单管理',
    color: 'var(--order-color)'
  },
  {
    key: 'customer-service',
    icon: 'message-circle',
    title: '客服中心',
    color: 'var(--info-color)'
  },
  {
    key: 'marketing',
    icon: 'gift',
    title: '营销活动',
    color: 'var(--warning-color)'
  }
])

// 图表数据
const salesData = ref([])
const productData = ref([])

// 图表配置
const chartOptions = computed(() => ({
  responsive: true,
  plugins: {
    legend: {
      position: 'top'
    }
  },
  scales: {
    y: {
      beginAtZero: true
    }
  }
}))

const pieOptions = computed(() => ({
  responsive: true,
  plugins: {
    legend: {
      position: 'bottom'
    }
  }
}))

// 事件处理
const handleStatClick = (stat) => {
  switch (stat.key) {
    case 'revenue':
      uni.navigateTo({ url: '/pages-sub/finance/revenue' })
      break
    case 'orders':
      uni.navigateTo({ url: '/pages-sub/order/list' })
      break
    case 'products':
      uni.navigateTo({ url: '/pages-sub/product/list' })
      break
    case 'customers':
      uni.navigateTo({ url: '/pages-sub/customer/list' })
      break
  }
}

const handleQuickAction = (action) => {
  switch (action.key) {
    case 'add-product':
      uni.navigateTo({ url: '/pages-sub/product/add' })
      break
    case 'manage-orders':
      uni.navigateTo({ url: '/pages-sub/order/list' })
      break
    case 'customer-service':
      uni.navigateTo({ url: '/pages-sub/service/index' })
      break
    case 'marketing':
      uni.navigateTo({ url: '/pages-sub/marketing/index' })
      break
  }
}

// 生命周期
onMounted(async () => {
  await merchantStore.loadDashboardData()
  salesData.value = merchantStore.salesData
  productData.value = merchantStore.productData
})
</script>

<style lang="scss" scoped>
.merchant-dashboard {
  padding: var(--spacing-md);
  background: var(--background-secondary);
  min-height: 100vh;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.chart-section {
  margin-bottom: var(--spacing-lg);
  
  > * + * {
    margin-top: var(--spacing-md);
  }
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}
</style>
```

## 📊 数据统计组件

### 统计卡片组件
```vue
<template>
  <view class="stats-card" :style="cardStyle" @click="handleClick">
    <view class="stats-header">
      <text class="stats-title">{{ title }}</text>
      <view class="stats-trend" :class="trendClass">
        <text class="trend-text">{{ trend }}</text>
        <uv-icon :name="trendIcon" size="12" :color="trendColor" />
      </view>
    </view>
    
    <view class="stats-content">
      <text class="stats-value">{{ value }}</text>
      <text class="stats-subtitle" v-if="subtitle">{{ subtitle }}</text>
    </view>
    
    <view class="stats-footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: String,
  value: [String, Number],
  subtitle: String,
  trend: String,
  color: {
    type: String,
    default: 'var(--primary-color)'
  }
})

const emit = defineEmits(['click'])

// 计算属性
const cardStyle = computed(() => ({
  borderLeft: `4px solid ${props.color}`
}))

const trendClass = computed(() => {
  if (!props.trend) return ''
  const isPositive = props.trend.startsWith('+')
  return isPositive ? 'trend-positive' : 'trend-negative'
})

const trendIcon = computed(() => {
  if (!props.trend) return ''
  const isPositive = props.trend.startsWith('+')
  return isPositive ? 'arrow-up' : 'arrow-down'
})

const trendColor = computed(() => {
  if (!props.trend) return 'var(--text-tertiary)'
  const isPositive = props.trend.startsWith('+')
  return isPositive ? 'var(--success-color)' : 'var(--error-color)'
})

// 事件处理
const handleClick = () => {
  emit('click')
}
</script>

<style lang="scss" scoped>
.stats-card {
  background: var(--background-primary);
  border-radius: var(--border-radius-component);
  box-shadow: var(--shadow-card);
  padding: var(--spacing-md);
  transition: transform var(--transition-fast);
  
  &:active {
    transform: scale(0.98);
  }
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-sm);
  
  .stats-title {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    font-weight: var(--font-weight-medium);
  }
  
  .stats-trend {
    display: flex;
    align-items: center;
    gap: 2px;
    
    .trend-text {
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
    }
    
    &.trend-positive .trend-text {
      color: var(--success-color);
    }
    
    &.trend-negative .trend-text {
      color: var(--error-color);
    }
  }
}

.stats-content {
  .stats-value {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: var(--text-primary);
    display: block;
    line-height: 1.2;
  }
  
  .stats-subtitle {
    font-size: var(--font-size-xs);
    color: var(--text-tertiary);
    margin-top: var(--spacing-xs);
    display: block;
  }
}

.stats-footer {
  margin-top: var(--spacing-sm);
  padding-top: var(--spacing-sm);
  border-top: 1px solid var(--border-color-light);
}
</style>
```

### 图表卡片组件
```vue
<template>
  <view class="chart-card">
    <view class="chart-header">
      <text class="chart-title">{{ title }}</text>
      <view class="chart-actions" v-if="$slots.actions">
        <slot name="actions"></slot>
      </view>
    </view>
    
    <view class="chart-content">
      <slot></slot>
    </view>
    
    <view class="chart-footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script setup>
defineProps({
  title: String
})
</script>

<style lang="scss" scoped>
.chart-card {
  background: var(--background-primary);
  border-radius: var(--border-radius-card);
  box-shadow: var(--shadow-card);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color-light);
  
  .chart-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
  }
}

.chart-content {
  padding: var(--spacing-md);
  min-height: 200px;
}

.chart-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color-light);
  background: var(--background-secondary);
}
</style>
```

## 🛍️ 商品管理组件

### 商品列表组件
```vue
<template>
  <view class="product-management">
    <!-- 搜索和筛选 -->
    <view class="search-section">
      <uv-input
        v-model="searchKeyword"
        placeholder="搜索商品名称或编号"
        :customStyle="searchInputStyle"
        prefixIcon="search"
        :clearable="true"
        @change="handleSearch"
      />
      
      <view class="filter-buttons">
        <uv-button
          v-for="filter in filterOptions"
          :key="filter.key"
          :type="activeFilter === filter.key ? 'primary' : 'default'"
          size="small"
          @click="handleFilterChange(filter.key)"
        >
          {{ filter.label }}
        </uv-button>
      </view>
    </view>
    
    <!-- 商品列表 -->
    <uv-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <uv-list @scrolltolower="onLoadMore">
        <view
          v-for="product in productList"
          :key="product.id"
          class="product-item"
          @click="handleProductClick(product)"
        >
          <image :src="product.image" class="product-image" />
          
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <text class="product-sku">SKU: {{ product.sku }}</text>
            <view class="product-price">
              <text class="price-current">¥{{ product.price }}</text>
              <text class="price-original" v-if="product.originalPrice">
                ¥{{ product.originalPrice }}
              </text>
            </view>
            <view class="product-stats">
              <text class="stat-item">库存: {{ product.stock }}</text>
              <text class="stat-item">销量: {{ product.sales }}</text>
            </view>
          </view>
          
          <view class="product-actions">
            <view class="product-status" :class="`status-${product.status}`">
              {{ getStatusText(product.status) }}
            </view>
            
            <view class="action-buttons">
              <uv-button
                size="mini"
                type="primary"
                :plain="true"
                @click.stop="handleEdit(product)"
              >
                编辑
              </uv-button>
              
              <uv-button
                size="mini"
                :type="product.status === 1 ? 'warning' : 'success'"
                :plain="true"
                @click.stop="handleToggleStatus(product)"
              >
                {{ product.status === 1 ? '下架' : '上架' }}
              </uv-button>
            </view>
          </view>
        </view>
        
        <uv-load-more :status="loadStatus" />
      </uv-list>
    </uv-pull-refresh>
    
    <!-- 浮动操作按钮 -->
    <view class="fab-container">
      <uv-button
        type="primary"
        shape="circle"
        size="large"
        icon="plus"
        @click="handleAddProduct"
      />
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useProductList } from '@/composables/useProductList'

// 组合式函数
const {
  productList,
  loading,
  refreshing,
  loadStatus,
  loadProductList,
  refreshProductList,
  loadMoreProducts
} = useProductList()

// 搜索和筛选
const searchKeyword = ref('')
const activeFilter = ref('all')

const filterOptions = [
  { key: 'all', label: '全部' },
  { key: 'on_sale', label: '在售' },
  { key: 'off_sale', label: '下架' },
  { key: 'low_stock', label: '库存不足' }
]

// 样式配置
const searchInputStyle = computed(() => ({
  backgroundColor: 'var(--background-primary)',
  borderRadius: 'var(--border-radius-component)',
  border: '1px solid var(--border-color)'
}))

// 事件处理
const handleSearch = (value) => {
  // 搜索逻辑
  loadProductList({ keyword: value, filter: activeFilter.value })
}

const handleFilterChange = (filter) => {
  activeFilter.value = filter
  loadProductList({ keyword: searchKeyword.value, filter })
}

const onRefresh = async () => {
  await refreshProductList()
}

const onLoadMore = async () => {
  await loadMoreProducts()
}

const handleProductClick = (product) => {
  uni.navigateTo({
    url: `/pages-sub/product/detail?id=${product.id}`
  })
}

const handleEdit = (product) => {
  uni.navigateTo({
    url: `/pages-sub/product/edit?id=${product.id}`
  })
}

const handleToggleStatus = async (product) => {
  const action = product.status === 1 ? '下架' : '上架'
  const confirm = await uni.showModal({
    title: '确认操作',
    content: `确定要${action}该商品吗？`
  })
  
  if (confirm.confirm) {
    // 切换商品状态
    await toggleProductStatus(product.id)
    await refreshProductList()
  }
}

const handleAddProduct = () => {
  uni.navigateTo({
    url: '/pages-sub/product/add'
  })
}

const getStatusText = (status) => {
  const statusMap = {
    0: '草稿',
    1: '在售',
    2: '下架',
    3: '售罄'
  }
  return statusMap[status] || '未知'
}

// 生命周期
onMounted(() => {
  loadProductList()
})
</script>

<style lang="scss" scoped>
.product-management {
  background: var(--background-secondary);
  min-height: 100vh;
  padding-bottom: 80px; // 为FAB留出空间
}

.search-section {
  padding: var(--spacing-md);
  background: var(--background-primary);
  margin-bottom: var(--spacing-sm);
  
  .filter-buttons {
    display: flex;
    gap: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    flex-wrap: wrap;
  }
}

.product-item {
  background: var(--background-primary);
  margin: 0 var(--spacing-md) var(--spacing-sm);
  border-radius: var(--border-radius-component);
  box-shadow: var(--shadow-card);
  padding: var(--spacing-md);
  display: flex;
  gap: var(--spacing-md);
  transition: transform var(--transition-fast);
  
  &:active {
    transform: scale(0.98);
  }
  
  .product-image {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius-small);
    flex-shrink: 0;
  }
  
  .product-info {
    flex: 1;
    min-width: 0;
    
    .product-name {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      color: var(--text-primary);
      display: block;
      margin-bottom: var(--spacing-xs);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .product-sku {
      font-size: var(--font-size-xs);
      color: var(--text-tertiary);
      display: block;
      margin-bottom: var(--spacing-xs);
    }
    
    .product-price {
      display: flex;
      align-items: baseline;
      gap: var(--spacing-xs);
      margin-bottom: var(--spacing-xs);
      
      .price-current {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--primary-color);
      }
      
      .price-original {
        font-size: var(--font-size-sm);
        color: var(--text-tertiary);
        text-decoration: line-through;
      }
    }
    
    .product-stats {
      display: flex;
      gap: var(--spacing-md);
      
      .stat-item {
        font-size: var(--font-size-xs);
        color: var(--text-secondary);
      }
    }
  }
  
  .product-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: var(--spacing-sm);
    
    .product-status {
      font-size: var(--font-size-xs);
      padding: 2px 6px;
      border-radius: var(--border-radius-mini);
      
      &.status-0 {
        color: var(--text-tertiary);
        background: var(--background-tertiary);
      }
      
      &.status-1 {
        color: var(--success-color);
        background: rgba(82, 196, 26, 0.1);
      }
      
      &.status-2 {
        color: var(--warning-color);
        background: rgba(250, 173, 20, 0.1);
      }
      
      &.status-3 {
        color: var(--error-color);
        background: rgba(245, 34, 45, 0.1);
      }
    }
    
    .action-buttons {
      display: flex;
      gap: var(--spacing-xs);
    }
  }
}

.fab-container {
  position: fixed;
  right: var(--spacing-md);
  bottom: calc(var(--spacing-md) + env(safe-area-inset-bottom));
  z-index: 999;
}
</style>
```

## 📋 商家端组件开发最佳实践

### 1. 商家认证集成
- 严格区分/before/（公开）和/back/（认证）接口
- 使用request.getAttribute("sysUserId")获取商家ID
- 实现完善的权限验证机制

### 2. 数据可视化
- 使用图表组件展示业务数据
- 提供多维度的数据分析视图
- 支持数据导出和报表功能

### 3. 批量操作
- 支持商品、订单的批量操作
- 提供进度提示和错误处理
- 实现操作撤销功能

### 4. 移动端优化
- 适配小屏幕的操作界面
- 优化触摸交互体验
- 支持手势操作

### 5. 性能优化
- 使用虚拟滚动处理大数据量
- 实现数据懒加载和分页
- 优化图表渲染性能

---

**文档说明**: 本文档为八闽助业集市商家端小程序组件开发标准，基于UniApp + Vue3技术栈，专注于商家业务场景的组件设计和开发规范。
