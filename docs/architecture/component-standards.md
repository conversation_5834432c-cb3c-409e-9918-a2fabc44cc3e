# 八闽助业集市H5端组件开发标准

## 🏗️ H5端技术架构

### 技术栈组合
```
┌─────────────────────────────────────────────────────────────────┐
│                    H5端技术栈                                   │
├─────────────────────────────────────────────────────────────────┤
│  UniApp          → 跨平台开发框架                               │
│  Vue3            → 渐进式JavaScript框架                         │
│  Composition API → Vue3组合式API                               │
│  微信JS-SDK      → 微信公众号开发工具包                         │
│  Pinia           → Vue3状态管理库                              │
│  luch-request    → 网络请求库                                  │
│  现代卡片式设计   → 与C端保持一致的设计语言                      │
└─────────────────────────────────────────────────────────────────┘
```

### H5端特色功能
- **微信集成**: 深度集成微信JS-SDK功能
- **企业微信对接**: 支持企业微信登录和功能
- **分享功能**: 微信分享、朋友圈分享等
- **支付集成**: 微信支付H5版本
- **地理位置**: 基于微信的位置服务

## 🎨 H5端设计系统

### 设计令牌（继承C端）
```scss
// h5-design-tokens.scss
:root {
  /* ========== 继承C端基础设计令牌 ========== */
  --primary-color: #667eea;
  --primary-light: #8fa4f3;
  --primary-dark: #4c63d2;
  --border-radius-card: 16px;
  --border-radius-component: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.06);
  
  /* ========== H5端专用配置 ========== */
  --h5-header-height: 44px;
  --h5-safe-area-top: env(safe-area-inset-top);
  --h5-safe-area-bottom: env(safe-area-inset-bottom);
  
  /* ========== 微信主题色彩 ========== */
  --wechat-green: #07c160;
  --wechat-blue: #1aad19;
  --wechat-orange: #fa9d3b;
  --wechat-red: #e64340;
  
  /* ========== 响应式断点 ========== */
  --breakpoint-mobile: 375px;
  --breakpoint-tablet: 768px;
  --breakpoint-desktop: 1024px;
}
```

### 微信JS-SDK集成组件
```vue
<template>
  <view class="wechat-integration">
    <!-- 微信分享组件 -->
    <view class="share-section">
      <button @click="shareToFriend" class="share-button friend">
        <uv-icon name="wechat" size="24" color="var(--wechat-green)" />
        <text>分享给好友</text>
      </button>
      
      <button @click="shareToTimeline" class="share-button timeline">
        <uv-icon name="moments" size="24" color="var(--wechat-blue)" />
        <text>分享到朋友圈</text>
      </button>
    </view>
    
    <!-- 位置服务组件 -->
    <view class="location-section">
      <button @click="getLocation" class="location-button" :loading="locationLoading">
        <uv-icon name="location" size="20" />
        <text>获取当前位置</text>
      </button>
      
      <view class="location-info" v-if="currentLocation">
        <text class="location-text">{{ currentLocation.address }}</text>
      </view>
    </view>
    
    <!-- 微信支付组件 -->
    <view class="payment-section">
      <button @click="wechatPay" class="pay-button" :loading="paymentLoading">
        <uv-icon name="wechat-pay" size="20" color="var(--wechat-green)" />
        <text>微信支付</text>
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useWechatSDK } from '@/composables/useWechatSDK'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()

// 响应式数据
const locationLoading = ref(false)
const paymentLoading = ref(false)
const currentLocation = ref(null)

// 微信SDK组合式函数
const {
  isWechatReady,
  initWechatSDK,
  configWechatShare,
  getWechatLocation,
  callWechatPay
} = useWechatSDK()

// 分享给好友
const shareToFriend = async () => {
  if (!isWechatReady.value) {
    uni.showToast({ title: '微信环境未就绪', icon: 'error' })
    return
  }
  
  try {
    await configWechatShare({
      title: '八闽助业集市',
      desc: '发现优质商品，享受便捷购物',
      link: window.location.href,
      imgUrl: 'https://example.com/share-icon.png',
      type: 'link',
      dataUrl: '',
      success: () => {
        console.log('分享成功')
      }
    })
    
    // 触发微信分享
    wx.ready(() => {
      wx.updateAppMessageShareData({
        title: '八闽助业集市',
        desc: '发现优质商品，享受便捷购物',
        link: window.location.href,
        imgUrl: 'https://example.com/share-icon.png'
      })
    })
  } catch (error) {
    uni.showToast({ title: '分享失败', icon: 'error' })
  }
}

// 分享到朋友圈
const shareToTimeline = async () => {
  if (!isWechatReady.value) {
    uni.showToast({ title: '微信环境未就绪', icon: 'error' })
    return
  }
  
  try {
    wx.ready(() => {
      wx.updateTimelineShareData({
        title: '八闽助业集市 - 发现优质商品',
        link: window.location.href,
        imgUrl: 'https://example.com/share-icon.png'
      })
    })
  } catch (error) {
    uni.showToast({ title: '分享失败', icon: 'error' })
  }
}

// 获取位置
const getLocation = async () => {
  if (!isWechatReady.value) {
    uni.showToast({ title: '微信环境未就绪', icon: 'error' })
    return
  }
  
  try {
    locationLoading.value = true
    const location = await getWechatLocation()
    currentLocation.value = location
    
    uni.showToast({ title: '位置获取成功', icon: 'success' })
  } catch (error) {
    uni.showToast({ title: '位置获取失败', icon: 'error' })
  } finally {
    locationLoading.value = false
  }
}

// 微信支付
const wechatPay = async () => {
  if (!userStore.isLogin) {
    uni.navigateTo({ url: '/pages/user/login' })
    return
  }
  
  try {
    paymentLoading.value = true
    
    // 创建支付订单
    const paymentData = await createPaymentOrder({
      amount: 100, // 示例金额
      description: '商品购买'
    })
    
    // 调用微信支付
    const result = await callWechatPay(paymentData)
    
    if (result.success) {
      uni.showToast({ title: '支付成功', icon: 'success' })
      // 处理支付成功逻辑
    } else {
      throw new Error(result.message || '支付失败')
    }
  } catch (error) {
    uni.showToast({ title: error.message || '支付失败', icon: 'error' })
  } finally {
    paymentLoading.value = false
  }
}

// 生命周期
onMounted(async () => {
  await initWechatSDK()
})
</script>

<style lang="scss" scoped>
.wechat-integration {
  padding: var(--spacing-md);
}

.share-section,
.location-section,
.payment-section {
  margin-bottom: var(--spacing-lg);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.share-button,
.location-button,
.pay-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  width: 100%;
  padding: var(--spacing-md);
  border-radius: var(--border-radius-component);
  border: none;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
  
  &:active {
    transform: scale(0.98);
  }
}

.share-button {
  &.friend {
    background: rgba(7, 193, 96, 0.1);
    color: var(--wechat-green);
    border: 1px solid var(--wechat-green);
  }
  
  &.timeline {
    background: rgba(26, 173, 25, 0.1);
    color: var(--wechat-blue);
    border: 1px solid var(--wechat-blue);
  }
}

.location-button {
  background: var(--background-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-card);
}

.location-info {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: var(--background-secondary);
  border-radius: var(--border-radius-small);
  
  .location-text {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
  }
}

.pay-button {
  background: var(--wechat-green);
  color: white;
  box-shadow: var(--shadow-medium);
  
  &:hover {
    background: var(--wechat-blue);
  }
}
</style>
```

## 🔧 微信JS-SDK组合式函数

### useWechatSDK组合式函数
```javascript
// composables/useWechatSDK.js
import { ref, computed } from 'vue'
import { wechatApi } from '@/api/wechat'

export function useWechatSDK() {
  const isWechatReady = ref(false)
  const isWechatEnv = ref(false)
  const sdkConfig = ref({})
  
  // 检查是否在微信环境
  const checkWechatEnv = () => {
    const ua = navigator.userAgent.toLowerCase()
    isWechatEnv.value = ua.includes('micromessenger')
    return isWechatEnv.value
  }
  
  // 初始化微信SDK
  const initWechatSDK = async () => {
    if (!checkWechatEnv()) {
      console.warn('非微信环境，跳过SDK初始化')
      return false
    }
    
    try {
      // 获取微信配置
      const response = await wechatApi.getJSSDKConfig({
        url: window.location.href.split('#')[0]
      })
      
      if (response.success) {
        sdkConfig.value = response.result
        
        // 配置微信SDK
        wx.config({
          debug: process.env.NODE_ENV === 'development',
          appId: sdkConfig.value.appId,
          timestamp: sdkConfig.value.timestamp,
          nonceStr: sdkConfig.value.nonceStr,
          signature: sdkConfig.value.signature,
          jsApiList: [
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'onMenuShareTimeline',
            'onMenuShareAppMessage',
            'chooseWXPay',
            'getLocation',
            'openLocation',
            'scanQRCode',
            'chooseImage',
            'uploadImage',
            'downloadImage',
            'previewImage'
          ]
        })
        
        // 监听配置结果
        wx.ready(() => {
          isWechatReady.value = true
          console.log('微信SDK初始化成功')
        })
        
        wx.error((res) => {
          console.error('微信SDK配置失败:', res)
          isWechatReady.value = false
        })
        
        return true
      } else {
        throw new Error(response.message || '获取微信配置失败')
      }
    } catch (error) {
      console.error('初始化微信SDK失败:', error)
      return false
    }
  }
  
  // 配置分享
  const configWechatShare = async (shareData) => {
    if (!isWechatReady.value) {
      throw new Error('微信SDK未就绪')
    }
    
    return new Promise((resolve, reject) => {
      wx.ready(() => {
        // 分享给朋友
        wx.updateAppMessageShareData({
          title: shareData.title,
          desc: shareData.desc,
          link: shareData.link,
          imgUrl: shareData.imgUrl,
          success: () => {
            console.log('分享给朋友配置成功')
            resolve({ type: 'friend', success: true })
          },
          fail: (error) => {
            console.error('分享给朋友配置失败:', error)
            reject(error)
          }
        })
        
        // 分享到朋友圈
        wx.updateTimelineShareData({
          title: shareData.title,
          link: shareData.link,
          imgUrl: shareData.imgUrl,
          success: () => {
            console.log('分享到朋友圈配置成功')
            resolve({ type: 'timeline', success: true })
          },
          fail: (error) => {
            console.error('分享到朋友圈配置失败:', error)
            reject(error)
          }
        })
      })
    })
  }
  
  // 获取地理位置
  const getWechatLocation = () => {
    if (!isWechatReady.value) {
      throw new Error('微信SDK未就绪')
    }
    
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'wgs84',
        success: (res) => {
          const location = {
            latitude: res.latitude,
            longitude: res.longitude,
            speed: res.speed,
            accuracy: res.accuracy
          }
          
          // 获取地址信息
          getAddressByLocation(location.latitude, location.longitude)
            .then(address => {
              location.address = address
              resolve(location)
            })
            .catch(() => {
              resolve(location)
            })
        },
        fail: (error) => {
          console.error('获取位置失败:', error)
          reject(error)
        }
      })
    })
  }
  
  // 调用微信支付
  const callWechatPay = (paymentData) => {
    if (!isWechatReady.value) {
      throw new Error('微信SDK未就绪')
    }
    
    return new Promise((resolve, reject) => {
      wx.chooseWXPay({
        timestamp: paymentData.timestamp,
        nonceStr: paymentData.nonceStr,
        package: paymentData.package,
        signType: paymentData.signType,
        paySign: paymentData.paySign,
        success: (res) => {
          console.log('微信支付成功:', res)
          resolve({ success: true, result: res })
        },
        fail: (error) => {
          console.error('微信支付失败:', error)
          reject({ success: false, error })
        }
      })
    })
  }
  
  // 扫描二维码
  const scanQRCode = () => {
    if (!isWechatReady.value) {
      throw new Error('微信SDK未就绪')
    }
    
    return new Promise((resolve, reject) => {
      wx.scanQRCode({
        needResult: 1,
        scanType: ['qrCode', 'barCode'],
        success: (res) => {
          resolve(res.resultStr)
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }
  
  // 选择图片
  const chooseWechatImage = (options = {}) => {
    if (!isWechatReady.value) {
      throw new Error('微信SDK未就绪')
    }
    
    return new Promise((resolve, reject) => {
      wx.chooseImage({
        count: options.count || 1,
        sizeType: options.sizeType || ['original', 'compressed'],
        sourceType: options.sourceType || ['album', 'camera'],
        success: (res) => {
          resolve(res.localIds)
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }
  
  // 根据坐标获取地址
  const getAddressByLocation = async (latitude, longitude) => {
    try {
      const response = await wechatApi.getAddressByLocation({
        latitude,
        longitude
      })
      
      if (response.success) {
        return response.result.address
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      console.error('获取地址失败:', error)
      return `${latitude}, ${longitude}`
    }
  }
  
  return {
    // 状态
    isWechatReady: computed(() => isWechatReady.value),
    isWechatEnv: computed(() => isWechatEnv.value),
    sdkConfig: computed(() => sdkConfig.value),
    
    // 方法
    checkWechatEnv,
    initWechatSDK,
    configWechatShare,
    getWechatLocation,
    callWechatPay,
    scanQRCode,
    chooseWechatImage
  }
}
```

## 📱 响应式布局组件

### H5响应式容器
```vue
<template>
  <view class="h5-responsive-container" :class="containerClass">
    <view class="container-header" v-if="showHeader">
      <slot name="header">
        <view class="default-header">
          <text class="header-title">{{ title }}</text>
        </view>
      </slot>
    </view>
    
    <view class="container-content" :style="contentStyle">
      <slot></slot>
    </view>
    
    <view class="container-footer" v-if="showFooter">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  title: String,
  showHeader: {
    type: Boolean,
    default: false
  },
  showFooter: {
    type: Boolean,
    default: false
  },
  maxWidth: {
    type: String,
    default: '100%'
  },
  padding: {
    type: String,
    default: 'var(--spacing-md)'
  }
})

// 响应式数据
const screenWidth = ref(0)
const screenHeight = ref(0)

// 计算属性
const containerClass = computed(() => {
  const classes = []
  
  if (screenWidth.value >= 768) {
    classes.push('container-tablet')
  }
  
  if (screenWidth.value >= 1024) {
    classes.push('container-desktop')
  }
  
  return classes
})

const contentStyle = computed(() => ({
  maxWidth: props.maxWidth,
  padding: props.padding,
  minHeight: `calc(100vh - ${props.showHeader ? 'var(--h5-header-height)' : '0px'} - ${props.showFooter ? '60px' : '0px'})`
}))

// 更新屏幕尺寸
const updateScreenSize = () => {
  screenWidth.value = window.innerWidth
  screenHeight.value = window.innerHeight
}

// 生命周期
onMounted(() => {
  updateScreenSize()
  window.addEventListener('resize', updateScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenSize)
})
</script>

<style lang="scss" scoped>
.h5-responsive-container {
  min-height: 100vh;
  background: var(--background-secondary);
  
  // 平板样式
  &.container-tablet {
    .container-content {
      max-width: 768px;
      margin: 0 auto;
    }
  }
  
  // 桌面样式
  &.container-desktop {
    .container-content {
      max-width: 1024px;
      margin: 0 auto;
      padding: var(--spacing-lg);
    }
  }
}

.container-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: var(--background-primary);
  border-bottom: 1px solid var(--border-color-light);
  
  .default-header {
    height: var(--h5-header-height);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 var(--spacing-md);
    
    .header-title {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--text-primary);
    }
  }
}

.container-content {
  flex: 1;
  width: 100%;
}

.container-footer {
  background: var(--background-primary);
  border-top: 1px solid var(--border-color-light);
  padding: var(--spacing-md);
}

// 响应式媒体查询
@media (max-width: 375px) {
  .h5-responsive-container {
    .container-content {
      padding: var(--spacing-sm);
    }
  }
}

@media (min-width: 768px) {
  .h5-responsive-container {
    .container-content {
      padding: var(--spacing-lg);
    }
  }
}

@media (min-width: 1024px) {
  .h5-responsive-container {
    background: var(--background-tertiary);
    
    .container-content {
      background: var(--background-primary);
      border-radius: var(--border-radius-card);
      box-shadow: var(--shadow-elevated);
      margin-top: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
    }
  }
}
</style>
```

## 🔐 企业微信集成组件

### 企业微信登录组件
```vue
<template>
  <view class="enterprise-wechat-login">
    <view class="login-header">
      <image src="/static/images/enterprise-logo.png" class="enterprise-logo" />
      <text class="login-title">企业微信登录</text>
      <text class="login-subtitle">使用企业微信账号快速登录</text>
    </view>
    
    <view class="login-content">
      <button 
        @click="enterpriseWechatLogin" 
        class="enterprise-login-button"
        :loading="loginLoading"
      >
        <uv-icon name="enterprise-wechat" size="24" color="white" />
        <text>企业微信登录</text>
      </button>
      
      <view class="login-tips">
        <text class="tip-text">仅限企业内部员工使用</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import { useEnterpriseWechat } from '@/composables/useEnterpriseWechat'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const loginLoading = ref(false)

// 企业微信组合式函数
const {
  isEnterpriseWechatEnv,
  initEnterpriseWechat,
  getEnterpriseUserInfo
} = useEnterpriseWechat()

// 企业微信登录
const enterpriseWechatLogin = async () => {
  try {
    loginLoading.value = true
    
    // 检查企业微信环境
    if (!isEnterpriseWechatEnv.value) {
      uni.showToast({
        title: '请在企业微信中打开',
        icon: 'error'
      })
      return
    }
    
    // 初始化企业微信
    await initEnterpriseWechat()
    
    // 获取用户信息
    const userInfo = await getEnterpriseUserInfo()
    
    // 调用登录接口
    const loginResult = await userStore.enterpriseWechatLogin(userInfo)
    
    if (loginResult) {
      uni.showToast({
        title: '登录成功',
        icon: 'success'
      })
      
      // 跳转到首页
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }
  } catch (error) {
    uni.showToast({
      title: error.message || '登录失败',
      icon: 'error'
    })
  } finally {
    loginLoading.value = false
  }
}
</script>

<style lang="scss" scoped>
.enterprise-wechat-login {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
}

.login-header {
  text-align: center;
  margin-bottom: var(--spacing-xxl);
  
  .enterprise-logo {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius-component);
    margin-bottom: var(--spacing-lg);
  }
  
  .login-title {
    font-size: var(--font-size-xxl);
    font-weight: var(--font-weight-bold);
    color: white;
    display: block;
    margin-bottom: var(--spacing-sm);
  }
  
  .login-subtitle {
    font-size: var(--font-size-md);
    color: rgba(255, 255, 255, 0.8);
    display: block;
  }
}

.login-content {
  width: 100%;
  max-width: 300px;
  
  .enterprise-login-button {
    width: 100%;
    height: 48px;
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: var(--border-radius-component);
    color: white;
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    backdrop-filter: blur(10px);
    transition: all var(--transition-normal);
    
    &:active {
      transform: scale(0.98);
      background: rgba(255, 255, 255, 0.3);
    }
  }
  
  .login-tips {
    text-align: center;
    margin-top: var(--spacing-lg);
    
    .tip-text {
      font-size: var(--font-size-sm);
      color: rgba(255, 255, 255, 0.7);
    }
  }
}
</style>
```

## 📋 H5端组件开发最佳实践

### 1. 微信环境适配
- 检测微信浏览器环境
- 正确配置微信JS-SDK
- 处理微信分享和支付功能
- 适配企业微信环境

### 2. 响应式设计
- 支持多种屏幕尺寸
- 使用CSS媒体查询
- 合理的触摸目标大小
- 考虑横竖屏切换

### 3. 性能优化
- 图片懒加载和压缩
- 代码分割和按需加载
- 缓存策略优化
- 减少重绘和回流

### 4. 用户体验
- 流畅的页面过渡
- 合理的加载状态
- 友好的错误提示
- 离线功能支持

### 5. 安全性
- HTTPS协议使用
- 数据传输加密
- XSS和CSRF防护
- 敏感信息保护

---

**文档说明**: 本文档为八闽助业集市H5端组件开发标准，基于UniApp + Vue3 + 微信JS-SDK技术栈，专注于微信公众号和企业微信环境的组件开发规范。
