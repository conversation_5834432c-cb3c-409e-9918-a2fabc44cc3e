# 八闽助业集市管理后台状态管理规范

## 🏗️ Vuex状态管理架构

### 状态管理结构
```
┌─────────────────────────────────────────────────────────────────┐
│                        Vuex Store                               │
├─────────────────────────────────────────────────────────────────┤
│  State (状态)    → 存储应用的状态数据                            │
│  Getters (计算)  → 从state中派生出一些状态                       │
│  Mutations (变更) → 更改state的唯一方法                          │
│  Actions (动作)  → 提交mutation，可以包含异步操作                │
│  Modules (模块)  → 将store分割成模块                            │
└─────────────────────────────────────────────────────────────────┘
```

### 模块划分策略
```javascript
// src/store/index.js
import Vue from 'vue'
import Vuex from 'vuex'
import getters from './getters'

// 模块导入
import app from './modules/app'
import user from './modules/user'
import permission from './modules/permission'
import dict from './modules/dict'
import system from './modules/system'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,      // 应用全局状态
    user,     // 用户相关状态
    permission, // 权限相关状态
    dict,     // 数据字典状态
    system    // 系统配置状态
  },
  state: {},
  mutations: {},
  actions: {},
  getters
})

export default store
```

## 👤 用户模块状态管理

### 用户状态定义
```javascript
// src/store/modules/user.js
import { login, logout, getUserInfo } from '@/api/login'
import { ACCESS_TOKEN, USER_INFO } from '@/store/mutation-types'
import { welcome } from '@/utils/util'

const user = {
  state: {
    token: '',
    username: '',
    realname: '',
    avatar: '',
    roles: [],
    permissions: [],
    info: {}
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_USERNAME: (state, username) => {
      state.username = username
    },
    SET_REALNAME: (state, realname) => {
      state.realname = realname
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_INFO: (state, info) => {
      state.info = info
    }
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      return new Promise((resolve, reject) => {
        login(userInfo).then(response => {
          if (response.success) {
            const result = response.result
            const userToken = result.token
            
            // 存储token
            Vue.ls.set(ACCESS_TOKEN, userToken, 7 * 24 * 60 * 60 * 1000)
            commit('SET_TOKEN', userToken)
            
            resolve(response)
          } else {
            reject(response)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit }) {
      return new Promise((resolve, reject) => {
        getUserInfo().then(response => {
          if (response.success) {
            const result = response.result
            
            // 设置用户信息
            commit('SET_INFO', result)
            commit('SET_USERNAME', result.username)
            commit('SET_REALNAME', result.realname)
            commit('SET_AVATAR', result.avatar)
            
            // 设置角色和权限
            if (result.roles && result.roles.length > 0) {
              commit('SET_ROLES', result.roles)
              commit('SET_PERMISSIONS', result.permissions)
            } else {
              reject(new Error('用户角色为空'))
            }
            
            resolve(result)
          } else {
            reject(new Error('获取用户信息失败'))
          }
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 登出
    Logout({ commit, state }) {
      return new Promise((resolve) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          Vue.ls.remove(ACCESS_TOKEN)
          resolve()
        }).catch(() => {
          resolve()
        }).finally(() => {
          // 清除本地存储
          Vue.ls.remove(ACCESS_TOKEN)
          Vue.ls.remove(USER_INFO)
        })
      })
    }
  }
}

export default user
```

## 🔐 权限模块状态管理

### 权限状态定义
```javascript
// src/store/modules/permission.js
import { asyncRouterMap, constantRouterMap } from '@/config/router.config'

/**
 * 过滤账户是否拥有某一个权限，并将菜单从加载列表移除
 */
function hasPermission(permission, route) {
  if (route.meta && route.meta.permission) {
    let flag = false
    for (let i = 0, len = permission.length; i < len; i++) {
      flag = route.meta.permission.includes(permission[i])
      if (flag) {
        return true
      }
    }
    return false
  }
  return true
}

/**
 * 单账户多角色时，使用该方法可过滤角色不存在的菜单
 */
function hasRole(roles, route) {
  if (route.meta && route.meta.roles) {
    return route.meta.roles.includes(roles.id)
  } else {
    return true
  }
}

function filterAsyncRouter(routerMap, roles) {
  const accessedRouters = routerMap.filter(route => {
    if (hasPermission(roles.permissionList, route)) {
      if (route.children && route.children.length) {
        route.children = filterAsyncRouter(route.children, roles)
      }
      return true
    }
    return false
  })
  return accessedRouters
}

const permission = {
  state: {
    routers: constantRouterMap,
    addRouters: []
  },
  mutations: {
    SET_ROUTERS: (state, routers) => {
      state.addRouters = routers
      state.routers = constantRouterMap.concat(routers)
    }
  },
  actions: {
    GenerateRoutes({ commit }, data) {
      return new Promise(resolve => {
        const { roles } = data
        const accessedRouters = filterAsyncRouter(asyncRouterMap, roles)
        commit('SET_ROUTERS', accessedRouters)
        resolve()
      })
    }
  }
}

export default permission
```

## 📚 数据字典模块状态管理

### 字典状态定义
```javascript
// src/store/modules/dict.js
import { getDictItems } from '@/api/api'

const dict = {
  state: {
    dictItems: {}
  },
  mutations: {
    SET_DICT_ITEMS: (state, { dictCode, items }) => {
      state.dictItems = {
        ...state.dictItems,
        [dictCode]: items
      }
    },
    CLEAR_DICT_ITEMS: (state) => {
      state.dictItems = {}
    }
  },
  actions: {
    // 获取字典项
    GetDictItems({ commit, state }, dictCode) {
      return new Promise((resolve, reject) => {
        // 如果已经缓存，直接返回
        if (state.dictItems[dictCode]) {
          resolve(state.dictItems[dictCode])
          return
        }
        
        // 从服务器获取
        getDictItems(dictCode).then(response => {
          if (response.success) {
            const items = response.result
            commit('SET_DICT_ITEMS', { dictCode, items })
            resolve(items)
          } else {
            reject(response.message)
          }
        }).catch(error => {
          reject(error)
        })
      })
    },
    
    // 刷新字典项
    RefreshDictItems({ commit }, dictCode) {
      return new Promise((resolve, reject) => {
        getDictItems(dictCode).then(response => {
          if (response.success) {
            const items = response.result
            commit('SET_DICT_ITEMS', { dictCode, items })
            resolve(items)
          } else {
            reject(response.message)
          }
        }).catch(error => {
          reject(error)
        })
      })
    }
  }
}

export default dict
```

## 🎛️ 应用模块状态管理

### 应用状态定义
```javascript
// src/store/modules/app.js
import { SIDEBAR_TYPE, TOGGLE_MOBILE_TYPE, TOGGLE_NAV_THEME, TOGGLE_LAYOUT, TOGGLE_FIXED_HEADER, TOGGLE_FIXED_SIDEBAR, TOGGLE_CONTENT_WIDTH, TOGGLE_HIDE_HEADER, TOGGLE_COLOR, TOGGLE_WEAK, TOGGLE_MULTI_TAB } from '@/store/mutation-types'
import { loadLanguageAsync } from '@/locales'

const app = {
  state: {
    sidebar: true,
    device: 'desktop',
    theme: 'dark',
    layout: 'sidemenu',
    contentWidth: 'Fixed',
    fixedHeader: false,
    fixedSidebar: false,
    autoHideHeader: false,
    color: '#1890FF',
    weak: false,
    multiTab: true,
    lang: 'zh-CN',
    _antLocale: {}
  },
  mutations: {
    [SIDEBAR_TYPE]: (state, type) => {
      state.sidebar = type
      Vue.ls.set(SIDEBAR_TYPE, type)
    },
    [TOGGLE_MOBILE_TYPE]: (state, isMobile) => {
      state.device = isMobile ? 'mobile' : 'desktop'
    },
    [TOGGLE_NAV_THEME]: (state, theme) => {
      state.theme = theme
      Vue.ls.set(TOGGLE_NAV_THEME, theme)
    },
    [TOGGLE_LAYOUT]: (state, mode) => {
      state.layout = mode
      Vue.ls.set(TOGGLE_LAYOUT, mode)
    },
    [TOGGLE_FIXED_HEADER]: (state, mode) => {
      state.fixedHeader = mode
      Vue.ls.set(TOGGLE_FIXED_HEADER, mode)
    },
    [TOGGLE_FIXED_SIDEBAR]: (state, mode) => {
      state.fixedSidebar = mode
      Vue.ls.set(TOGGLE_FIXED_SIDEBAR, mode)
    },
    [TOGGLE_CONTENT_WIDTH]: (state, type) => {
      state.contentWidth = type
      Vue.ls.set(TOGGLE_CONTENT_WIDTH, type)
    },
    [TOGGLE_HIDE_HEADER]: (state, type) => {
      state.autoHideHeader = type
      Vue.ls.set(TOGGLE_HIDE_HEADER, type)
    },
    [TOGGLE_COLOR]: (state, color) => {
      state.color = color
      Vue.ls.set(TOGGLE_COLOR, color)
    },
    [TOGGLE_WEAK]: (state, mode) => {
      state.weak = mode
      Vue.ls.set(TOGGLE_WEAK, mode)
    },
    [TOGGLE_MULTI_TAB]: (state, bool) => {
      Vue.ls.set(TOGGLE_MULTI_TAB, bool)
      state.multiTab = bool
    },
    SET_LANG: (state, lang, antd = {}) => {
      state.lang = lang
      state._antLocale = antd
      Vue.ls.set('lang', lang)
    }
  },
  actions: {
    setLang({ commit }, lang) {
      return new Promise((resolve, reject) => {
        loadLanguageAsync(lang).then(antd => {
          commit('SET_LANG', lang, antd)
          resolve()
        }).catch((e) => {
          reject(e)
        })
      })
    }
  }
}

export default app
```

## 🔧 Getters全局计算属性

### 全局Getters定义
```javascript
// src/store/getters.js
const getters = {
  // 应用相关
  device: state => state.app.device,
  theme: state => state.app.theme,
  color: state => state.app.color,
  token: state => state.user.token,
  avatar: state => state.user.avatar,
  username: state => state.user.username,
  realname: state => state.user.realname,
  welcome: state => state.user.welcome,
  roles: state => state.user.roles,
  permissions: state => state.user.permissions,
  userInfo: state => state.user.info,
  addRouters: state => state.permission.addRouters,
  multiTab: state => state.app.multiTab,
  lang: state => state.app.lang,
  
  // 字典相关
  dictItems: state => state.dict.dictItems,
  
  // 权限判断
  hasPermission: (state) => (permission) => {
    return state.user.permissions.includes(permission)
  },
  
  // 角色判断
  hasRole: (state) => (role) => {
    return state.user.roles.some(r => r.roleCode === role)
  },
  
  // 字典项获取
  getDictText: (state) => (dictCode, value) => {
    const items = state.dict.dictItems[dictCode]
    if (items) {
      const item = items.find(item => item.value === value)
      return item ? item.text : value
    }
    return value
  }
}

export default getters
```

## 📝 组件中使用状态管理

### 在组件中使用Vuex
```vue
<template>
  <div class="user-profile">
    <a-card title="用户信息">
      <p>用户名: {{ username }}</p>
      <p>真实姓名: {{ realname }}</p>
      <p>角色: {{ roles.map(r => r.roleName).join(', ') }}</p>
      
      <a-button @click="handleLogout">退出登录</a-button>
    </a-card>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  name: 'UserProfile',
  computed: {
    // 映射state
    ...mapState('user', ['username', 'realname', 'roles']),
    
    // 映射getters
    ...mapGetters(['hasPermission', 'hasRole']),
    
    // 计算属性
    canEdit() {
      return this.hasPermission('user:edit')
    },
    
    isAdmin() {
      return this.hasRole('admin')
    }
  },
  methods: {
    // 映射actions
    ...mapActions('user', ['Logout']),
    
    // 处理登出
    async handleLogout() {
      try {
        await this.Logout()
        this.$router.push('/user/login')
        this.$message.success('退出登录成功')
      } catch (error) {
        this.$message.error('退出登录失败')
      }
    }
  }
}
</script>
```

### 在组件中使用字典
```vue
<template>
  <div class="dict-example">
    <a-select v-model="status" placeholder="请选择状态">
      <a-select-option 
        v-for="item in statusOptions" 
        :key="item.value" 
        :value="item.value"
      >
        {{ item.text }}
      </a-select-option>
    </a-select>
    
    <p>当前状态: {{ getDictText('user_status', status) }}</p>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'DictExample',
  data() {
    return {
      status: '',
      statusOptions: []
    }
  },
  computed: {
    ...mapGetters(['getDictText'])
  },
  methods: {
    ...mapActions('dict', ['GetDictItems'])
  },
  async created() {
    // 获取字典项
    try {
      this.statusOptions = await this.GetDictItems('user_status')
    } catch (error) {
      this.$message.error('获取字典项失败')
    }
  }
}
</script>
```

## 📋 状态管理最佳实践

### 1. 模块化管理
- 按功能模块划分store
- 使用命名空间避免冲突
- 保持模块职责单一

### 2. 状态设计原则
- 只存储全局共享的状态
- 避免存储可计算的状态
- 保持状态结构扁平化

### 3. 异步操作规范
- 在actions中处理异步操作
- 使用Promise处理异步结果
- 统一错误处理机制

### 4. 性能优化
- 合理使用getters缓存计算结果
- 避免在模板中直接访问深层状态
- 使用mapState等辅助函数简化代码

### 5. 调试和测试
- 使用Vue DevTools调试状态变化
- 为关键actions编写单元测试
- 记录重要的状态变更日志

---

**文档说明**: 本文档为八闽助业集市管理后台Vuex状态管理规范，所有状态管理必须严格遵循此规范。
