# 八闽助业集市商家端小程序状态管理规范

## 🏗️ 商家端Pinia状态管理架构

### 商家端状态管理特点
```
┌─────────────────────────────────────────────────────────────────┐
│                    商家端状态管理特色                            │
├─────────────────────────────────────────────────────────────────┤
│  商家认证     → 基于/before/和/back/的双重认证体系               │
│  业务数据     → 商品、订单、客户、财务等业务状态                 │
│  权限管理     → 商家角色和功能权限控制                          │
│  数据统计     → 实时业务数据和统计信息                          │
│  消息通知     → 订单、客服、系统消息管理                        │
└─────────────────────────────────────────────────────────────────┘
```

### Store模块划分
```javascript
// stores/index.js
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'

const pinia = createPinia()

// 持久化插件配置
pinia.use(
  createPersistedState({
    storage: {
      getItem: (key) => uni.getStorageSync(key),
      setItem: (key, value) => uni.setStorageSync(key, value),
      removeItem: (key) => uni.removeStorageSync(key)
    }
  })
)

export default pinia

// 导出所有store
export { useMerchantStore } from './modules/merchant'
export { useProductStore } from './modules/product'
export { useOrderStore } from './modules/order'
export { useCustomerStore } from './modules/customer'
export { useFinanceStore } from './modules/finance'
export { useMessageStore } from './modules/message'
export { useAppStore } from './modules/app'
```

## 🏪 商家状态管理

### 商家Store定义
```javascript
// stores/modules/merchant.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { merchantApi } from '@/api/merchant'
import { loginApi } from '@/api/login'

export const useMerchantStore = defineStore('merchant', () => {
  // 状态定义
  const token = ref('')
  const merchantInfo = ref({})
  const permissions = ref([])
  const dashboardData = ref({})
  
  // 计算属性
  const isLogin = computed(() => !!token.value)
  const merchantName = computed(() => merchantInfo.value.merchantName || '未设置')
  const avatar = computed(() => merchantInfo.value.avatar || '/static/images/default-merchant.png')
  const status = computed(() => merchantInfo.value.status || 0)
  const isActive = computed(() => status.value === 1)
  
  // 商家登录
  const login = async (loginData) => {
    try {
      const response = await loginApi.merchantLogin(loginData)
      if (response.success) {
        token.value = response.result.token
        merchantInfo.value = response.result.merchantInfo
        permissions.value = response.result.permissions || []
        
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        return response.result
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '登录失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 获取商家信息
  const getMerchantInfo = async () => {
    try {
      const response = await merchantApi.getMerchantInfo()
      if (response.success) {
        merchantInfo.value = response.result
        return response.result
      }
    } catch (error) {
      console.error('获取商家信息失败:', error)
    }
  }
  
  // 更新商家信息
  const updateMerchantInfo = async (data) => {
    try {
      const response = await merchantApi.updateMerchantInfo(data)
      if (response.success) {
        merchantInfo.value = { ...merchantInfo.value, ...data }
        uni.showToast({
          title: '更新成功',
          icon: 'success'
        })
        return response.result
      } else {
        throw new Error(response.message || '更新失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '更新失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 加载仪表盘数据
  const loadDashboardData = async () => {
    try {
      const response = await merchantApi.getDashboardData()
      if (response.success) {
        dashboardData.value = response.result
        return response.result
      }
    } catch (error) {
      console.error('加载仪表盘数据失败:', error)
    }
  }
  
  // 检查权限
  const hasPermission = (permission) => {
    return permissions.value.includes(permission)
  }
  
  // 退出登录
  const logout = async () => {
    try {
      if (token.value) {
        await loginApi.merchantLogout()
      }
    } catch (error) {
      console.error('退出登录失败:', error)
    } finally {
      // 清除本地数据
      token.value = ''
      merchantInfo.value = {}
      permissions.value = []
      dashboardData.value = {}
      
      // 清除其他store的商家相关数据
      const productStore = useProductStore()
      const orderStore = useOrderStore()
      productStore.clearData()
      orderStore.clearData()
      
      uni.showToast({
        title: '已退出登录',
        icon: 'success'
      })
      
      // 跳转到登录页
      uni.reLaunch({
        url: '/pages/login/index'
      })
    }
  }
  
  return {
    // 状态
    token,
    merchantInfo,
    permissions,
    dashboardData,
    
    // 计算属性
    isLogin,
    merchantName,
    avatar,
    status,
    isActive,
    
    // 方法
    login,
    getMerchantInfo,
    updateMerchantInfo,
    loadDashboardData,
    hasPermission,
    logout
  }
}, {
  // 持久化配置
  persist: {
    key: 'merchant-store',
    storage: {
      getItem: (key) => uni.getStorageSync(key),
      setItem: (key, value) => uni.setStorageSync(key, value),
      removeItem: (key) => uni.removeStorageSync(key)
    },
    paths: ['token', 'merchantInfo', 'permissions']
  }
})
```

## 📦 商品状态管理

### 商品Store定义
```javascript
// stores/modules/product.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { productApi } from '@/api/product'
import { useMerchantStore } from './merchant'

export const useProductStore = defineStore('product', () => {
  const merchantStore = useMerchantStore()
  
  // 状态定义
  const productList = ref([])
  const categories = ref([])
  const loading = ref(false)
  const currentProduct = ref({})
  
  // 计算属性
  const totalProducts = computed(() => productList.value.length)
  const onSaleProducts = computed(() => 
    productList.value.filter(item => item.status === 1)
  )
  const offSaleProducts = computed(() => 
    productList.value.filter(item => item.status === 2)
  )
  const lowStockProducts = computed(() => 
    productList.value.filter(item => item.stock < 10)
  )
  
  // 获取商品列表
  const getProductList = async (params = {}) => {
    if (!merchantStore.isLogin) return
    
    try {
      loading.value = true
      const response = await productApi.getMerchantProductList(params)
      if (response.success) {
        productList.value = response.result.records || []
        return response.result
      }
    } catch (error) {
      console.error('获取商品列表失败:', error)
    } finally {
      loading.value = false
    }
  }
  
  // 获取商品分类
  const getCategories = async () => {
    try {
      const response = await productApi.getCategories()
      if (response.success) {
        categories.value = response.result
        return response.result
      }
    } catch (error) {
      console.error('获取商品分类失败:', error)
    }
  }
  
  // 添加商品
  const addProduct = async (productData) => {
    try {
      const response = await productApi.addProduct(productData)
      if (response.success) {
        // 更新本地列表
        await getProductList()
        
        uni.showToast({
          title: '商品添加成功',
          icon: 'success'
        })
        
        return response.result
      } else {
        throw new Error(response.message || '添加失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '添加失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 更新商品
  const updateProduct = async (productId, productData) => {
    try {
      const response = await productApi.updateProduct(productId, productData)
      if (response.success) {
        // 更新本地数据
        const index = productList.value.findIndex(item => item.id === productId)
        if (index > -1) {
          productList.value[index] = { ...productList.value[index], ...productData }
        }
        
        uni.showToast({
          title: '商品更新成功',
          icon: 'success'
        })
        
        return response.result
      } else {
        throw new Error(response.message || '更新失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '更新失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 删除商品
  const deleteProduct = async (productId) => {
    try {
      const response = await productApi.deleteProduct(productId)
      if (response.success) {
        // 更新本地数据
        const index = productList.value.findIndex(item => item.id === productId)
        if (index > -1) {
          productList.value.splice(index, 1)
        }
        
        uni.showToast({
          title: '商品删除成功',
          icon: 'success'
        })
        
        return response.result
      } else {
        throw new Error(response.message || '删除失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '删除失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 切换商品状态
  const toggleProductStatus = async (productId) => {
    try {
      const product = productList.value.find(item => item.id === productId)
      if (!product) return
      
      const newStatus = product.status === 1 ? 2 : 1
      const response = await productApi.updateProductStatus(productId, newStatus)
      
      if (response.success) {
        // 更新本地数据
        product.status = newStatus
        
        const statusText = newStatus === 1 ? '上架' : '下架'
        uni.showToast({
          title: `商品${statusText}成功`,
          icon: 'success'
        })
        
        return response.result
      } else {
        throw new Error(response.message || '操作失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '操作失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 批量操作商品
  const batchUpdateProducts = async (productIds, updateData) => {
    try {
      const response = await productApi.batchUpdateProducts(productIds, updateData)
      if (response.success) {
        // 更新本地数据
        productIds.forEach(id => {
          const index = productList.value.findIndex(item => item.id === id)
          if (index > -1) {
            productList.value[index] = { ...productList.value[index], ...updateData }
          }
        })
        
        uni.showToast({
          title: '批量操作成功',
          icon: 'success'
        })
        
        return response.result
      } else {
        throw new Error(response.message || '批量操作失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '批量操作失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 获取商品详情
  const getProductDetail = async (productId) => {
    try {
      const response = await productApi.getProductDetail(productId)
      if (response.success) {
        currentProduct.value = response.result
        return response.result
      }
    } catch (error) {
      console.error('获取商品详情失败:', error)
    }
  }
  
  // 清除数据
  const clearData = () => {
    productList.value = []
    categories.value = []
    currentProduct.value = {}
  }
  
  return {
    // 状态
    productList,
    categories,
    loading,
    currentProduct,
    
    // 计算属性
    totalProducts,
    onSaleProducts,
    offSaleProducts,
    lowStockProducts,
    
    // 方法
    getProductList,
    getCategories,
    addProduct,
    updateProduct,
    deleteProduct,
    toggleProductStatus,
    batchUpdateProducts,
    getProductDetail,
    clearData
  }
})
```

## 📋 订单状态管理

### 订单Store定义
```javascript
// stores/modules/order.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { orderApi } from '@/api/order'
import { useMerchantStore } from './merchant'

export const useOrderStore = defineStore('order', () => {
  const merchantStore = useMerchantStore()
  
  // 状态定义
  const orderList = ref([])
  const loading = ref(false)
  const currentOrder = ref({})
  const orderStats = ref({})
  
  // 计算属性
  const totalOrders = computed(() => orderList.value.length)
  const pendingOrders = computed(() => 
    orderList.value.filter(item => item.status === 1)
  )
  const processingOrders = computed(() => 
    orderList.value.filter(item => item.status === 2)
  )
  const completedOrders = computed(() => 
    orderList.value.filter(item => item.status === 3)
  )
  
  // 获取订单列表
  const getOrderList = async (params = {}) => {
    if (!merchantStore.isLogin) return
    
    try {
      loading.value = true
      const response = await orderApi.getMerchantOrderList(params)
      if (response.success) {
        orderList.value = response.result.records || []
        return response.result
      }
    } catch (error) {
      console.error('获取订单列表失败:', error)
    } finally {
      loading.value = false
    }
  }
  
  // 获取订单统计
  const getOrderStats = async () => {
    try {
      const response = await orderApi.getOrderStats()
      if (response.success) {
        orderStats.value = response.result
        return response.result
      }
    } catch (error) {
      console.error('获取订单统计失败:', error)
    }
  }
  
  // 更新订单状态
  const updateOrderStatus = async (orderId, status, remark = '') => {
    try {
      const response = await orderApi.updateOrderStatus(orderId, { status, remark })
      if (response.success) {
        // 更新本地数据
        const order = orderList.value.find(item => item.id === orderId)
        if (order) {
          order.status = status
          order.remark = remark
        }
        
        const statusText = getStatusText(status)
        uni.showToast({
          title: `订单${statusText}成功`,
          icon: 'success'
        })
        
        return response.result
      } else {
        throw new Error(response.message || '操作失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '操作失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 批量处理订单
  const batchProcessOrders = async (orderIds, action) => {
    try {
      const response = await orderApi.batchProcessOrders(orderIds, action)
      if (response.success) {
        // 刷新订单列表
        await getOrderList()
        
        uni.showToast({
          title: '批量操作成功',
          icon: 'success'
        })
        
        return response.result
      } else {
        throw new Error(response.message || '批量操作失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '批量操作失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 获取订单详情
  const getOrderDetail = async (orderId) => {
    try {
      const response = await orderApi.getOrderDetail(orderId)
      if (response.success) {
        currentOrder.value = response.result
        return response.result
      }
    } catch (error) {
      console.error('获取订单详情失败:', error)
    }
  }
  
  // 发货
  const shipOrder = async (orderId, shippingData) => {
    try {
      const response = await orderApi.shipOrder(orderId, shippingData)
      if (response.success) {
        // 更新本地数据
        const order = orderList.value.find(item => item.id === orderId)
        if (order) {
          order.status = 2 // 已发货
          order.shippingInfo = shippingData
        }
        
        uni.showToast({
          title: '发货成功',
          icon: 'success'
        })
        
        return response.result
      } else {
        throw new Error(response.message || '发货失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '发货失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 获取状态文本
  const getStatusText = (status) => {
    const statusMap = {
      0: '待付款',
      1: '待发货',
      2: '已发货',
      3: '已完成',
      4: '已取消',
      5: '退款中',
      6: '已退款'
    }
    return statusMap[status] || '未知状态'
  }
  
  // 清除数据
  const clearData = () => {
    orderList.value = []
    currentOrder.value = {}
    orderStats.value = {}
  }
  
  return {
    // 状态
    orderList,
    loading,
    currentOrder,
    orderStats,
    
    // 计算属性
    totalOrders,
    pendingOrders,
    processingOrders,
    completedOrders,
    
    // 方法
    getOrderList,
    getOrderStats,
    updateOrderStatus,
    batchProcessOrders,
    getOrderDetail,
    shipOrder,
    getStatusText,
    clearData
  }
})
```

## 📱 在组件中使用Store

### 商家端组件使用示例
```vue
<template>
  <view class="merchant-dashboard">
    <view class="merchant-info" v-if="merchantStore.isLogin">
      <image :src="merchantStore.avatar" class="merchant-avatar" />
      <view class="merchant-details">
        <text class="merchant-name">{{ merchantStore.merchantName }}</text>
        <text class="merchant-status" :class="`status-${merchantStore.status}`">
          {{ getStatusText(merchantStore.status) }}
        </text>
      </view>
    </view>
    
    <view class="stats-overview">
      <view class="stat-item">
        <text class="stat-value">{{ productStore.totalProducts }}</text>
        <text class="stat-label">商品总数</text>
      </view>
      <view class="stat-item">
        <text class="stat-value">{{ orderStore.pendingOrders.length }}</text>
        <text class="stat-label">待处理订单</text>
      </view>
    </view>
    
    <view class="quick-actions">
      <button @click="handleAddProduct" v-if="merchantStore.hasPermission('product:add')">
        添加商品
      </button>
      <button @click="handleViewOrders" v-if="merchantStore.hasPermission('order:view')">
        查看订单
      </button>
    </view>
  </view>
</template>

<script setup>
import { onMounted } from 'vue'
import { useMerchantStore } from '@/stores/merchant'
import { useProductStore } from '@/stores/product'
import { useOrderStore } from '@/stores/order'

const merchantStore = useMerchantStore()
const productStore = useProductStore()
const orderStore = useOrderStore()

// 事件处理
const handleAddProduct = () => {
  uni.navigateTo({
    url: '/pages-sub/product/add'
  })
}

const handleViewOrders = () => {
  uni.navigateTo({
    url: '/pages-sub/order/list'
  })
}

const getStatusText = (status) => {
  const statusMap = {
    0: '待审核',
    1: '正常营业',
    2: '暂停营业',
    3: '已关闭'
  }
  return statusMap[status] || '未知'
}

// 页面加载时获取数据
onMounted(async () => {
  if (merchantStore.isLogin) {
    await Promise.all([
      merchantStore.loadDashboardData(),
      productStore.getProductList(),
      orderStore.getOrderList({ status: 1 }) // 只获取待处理订单
    ])
  }
})
</script>
```

## 📋 商家端状态管理最佳实践

### 1. 权限验证
- 在每个操作前检查商家权限
- 使用hasPermission方法验证功能权限
- 根据权限动态显示界面元素

### 2. 数据同步
- 操作成功后及时更新本地状态
- 使用乐观更新提升用户体验
- 处理并发操作的数据冲突

### 3. 错误处理
- 统一的错误提示机制
- 网络异常的重试机制
- 数据加载失败的降级处理

### 4. 性能优化
- 合理使用计算属性缓存
- 避免不必要的API调用
- 使用分页加载大数据量

### 5. 数据持久化
- 只持久化必要的状态数据
- 敏感信息的安全存储
- 定期清理过期数据

---

**文档说明**: 本文档为八闽助业集市商家端小程序Pinia状态管理规范，专注于商家业务场景的状态管理设计和最佳实践。
