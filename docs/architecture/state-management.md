# 八闽助业集市H5端状态管理规范

## 🏗️ H5端Pinia状态管理架构

### H5端状态管理特点
```
┌─────────────────────────────────────────────────────────────────┐
│                    H5端状态管理特色                              │
├─────────────────────────────────────────────────────────────────┤
│  微信集成     → 微信用户信息、分享状态、支付状态                 │
│  企业微信     → 企业用户认证、组织架构信息                      │
│  地理位置     → 用户位置信息、附近商家数据                      │
│  网络状态     → 在线/离线状态、网络类型检测                     │
│  设备信息     → 屏幕尺寸、设备类型、浏览器信息                  │
└─────────────────────────────────────────────────────────────────┘
```

### Store模块划分
```javascript
// stores/index.js
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'

const pinia = createPinia()

// H5端专用持久化配置
pinia.use(
  createPersistedState({
    storage: {
      getItem: (key) => {
        try {
          return localStorage.getItem(key)
        } catch (error) {
          console.warn('localStorage不可用，使用内存存储')
          return null
        }
      },
      setItem: (key, value) => {
        try {
          localStorage.setItem(key, value)
        } catch (error) {
          console.warn('localStorage不可用，跳过存储')
        }
      },
      removeItem: (key) => {
        try {
          localStorage.removeItem(key)
        } catch (error) {
          console.warn('localStorage不可用，跳过删除')
        }
      }
    }
  })
)

export default pinia

// 导出所有store
export { useUserStore } from './modules/user'
export { useWechatStore } from './modules/wechat'
export { useLocationStore } from './modules/location'
export { useDeviceStore } from './modules/device'
export { useCartStore } from './modules/cart'
export { useAppStore } from './modules/app'
```

## 📱 微信状态管理

### 微信Store定义
```javascript
// stores/modules/wechat.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { wechatApi } from '@/api/wechat'

export const useWechatStore = defineStore('wechat', () => {
  // 状态定义
  const isWechatEnv = ref(false)
  const isEnterpriseWechat = ref(false)
  const wechatReady = ref(false)
  const wechatConfig = ref({})
  const userInfo = ref({})
  const shareConfig = ref({})
  const paymentStatus = ref('')
  
  // 计算属性
  const canUseWechatFeatures = computed(() => isWechatEnv.value && wechatReady.value)
  const isWechatUser = computed(() => !!userInfo.value.openid)
  
  // 检测微信环境
  const detectWechatEnv = () => {
    const ua = navigator.userAgent.toLowerCase()
    isWechatEnv.value = ua.includes('micromessenger')
    isEnterpriseWechat.value = ua.includes('wxwork')
    
    return {
      isWechat: isWechatEnv.value,
      isEnterprise: isEnterpriseWechat.value
    }
  }
  
  // 初始化微信SDK
  const initWechatSDK = async () => {
    if (!isWechatEnv.value) {
      console.warn('非微信环境，跳过SDK初始化')
      return false
    }
    
    try {
      // 获取微信配置
      const response = await wechatApi.getJSSDKConfig({
        url: window.location.href.split('#')[0]
      })
      
      if (response.success) {
        wechatConfig.value = response.result
        
        // 配置微信SDK
        wx.config({
          debug: process.env.NODE_ENV === 'development',
          appId: wechatConfig.value.appId,
          timestamp: wechatConfig.value.timestamp,
          nonceStr: wechatConfig.value.nonceStr,
          signature: wechatConfig.value.signature,
          jsApiList: [
            'updateAppMessageShareData',
            'updateTimelineShareData',
            'chooseWXPay',
            'getLocation',
            'openLocation',
            'scanQRCode',
            'chooseImage',
            'previewImage',
            'downloadImage',
            'uploadImage'
          ]
        })
        
        // 监听配置结果
        wx.ready(() => {
          wechatReady.value = true
          console.log('微信SDK初始化成功')
        })
        
        wx.error((res) => {
          console.error('微信SDK配置失败:', res)
          wechatReady.value = false
        })
        
        return true
      } else {
        throw new Error(response.message || '获取微信配置失败')
      }
    } catch (error) {
      console.error('初始化微信SDK失败:', error)
      return false
    }
  }
  
  // 获取微信用户信息
  const getWechatUserInfo = async (code) => {
    try {
      const response = await wechatApi.getUserInfo(code)
      if (response.success) {
        userInfo.value = response.result
        return response.result
      } else {
        throw new Error(response.message || '获取用户信息失败')
      }
    } catch (error) {
      console.error('获取微信用户信息失败:', error)
      throw error
    }
  }
  
  // 配置分享
  const configShare = (shareData) => {
    if (!canUseWechatFeatures.value) {
      console.warn('微信功能不可用')
      return false
    }
    
    shareConfig.value = shareData
    
    wx.ready(() => {
      // 分享给朋友
      wx.updateAppMessageShareData({
        title: shareData.title,
        desc: shareData.desc,
        link: shareData.link,
        imgUrl: shareData.imgUrl,
        success: () => {
          console.log('分享给朋友配置成功')
        }
      })
      
      // 分享到朋友圈
      wx.updateTimelineShareData({
        title: shareData.title,
        link: shareData.link,
        imgUrl: shareData.imgUrl,
        success: () => {
          console.log('分享到朋友圈配置成功')
        }
      })
    })
    
    return true
  }
  
  // 微信支付
  const wechatPay = async (paymentData) => {
    if (!canUseWechatFeatures.value) {
      throw new Error('微信功能不可用')
    }
    
    return new Promise((resolve, reject) => {
      paymentStatus.value = 'paying'
      
      wx.chooseWXPay({
        timestamp: paymentData.timestamp,
        nonceStr: paymentData.nonceStr,
        package: paymentData.package,
        signType: paymentData.signType,
        paySign: paymentData.paySign,
        success: (res) => {
          paymentStatus.value = 'success'
          console.log('微信支付成功:', res)
          resolve({ success: true, result: res })
        },
        fail: (error) => {
          paymentStatus.value = 'failed'
          console.error('微信支付失败:', error)
          reject({ success: false, error })
        }
      })
    })
  }
  
  // 获取地理位置
  const getLocation = () => {
    if (!canUseWechatFeatures.value) {
      throw new Error('微信功能不可用')
    }
    
    return new Promise((resolve, reject) => {
      wx.getLocation({
        type: 'wgs84',
        success: (res) => {
          resolve({
            latitude: res.latitude,
            longitude: res.longitude,
            speed: res.speed,
            accuracy: res.accuracy
          })
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }
  
  // 扫描二维码
  const scanQRCode = () => {
    if (!canUseWechatFeatures.value) {
      throw new Error('微信功能不可用')
    }
    
    return new Promise((resolve, reject) => {
      wx.scanQRCode({
        needResult: 1,
        scanType: ['qrCode', 'barCode'],
        success: (res) => {
          resolve(res.resultStr)
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }
  
  // 选择图片
  const chooseImage = (options = {}) => {
    if (!canUseWechatFeatures.value) {
      throw new Error('微信功能不可用')
    }
    
    return new Promise((resolve, reject) => {
      wx.chooseImage({
        count: options.count || 1,
        sizeType: options.sizeType || ['original', 'compressed'],
        sourceType: options.sourceType || ['album', 'camera'],
        success: (res) => {
          resolve(res.localIds)
        },
        fail: (error) => {
          reject(error)
        }
      })
    })
  }
  
  // 清除微信数据
  const clearWechatData = () => {
    userInfo.value = {}
    shareConfig.value = {}
    paymentStatus.value = ''
  }
  
  return {
    // 状态
    isWechatEnv,
    isEnterpriseWechat,
    wechatReady,
    wechatConfig,
    userInfo,
    shareConfig,
    paymentStatus,
    
    // 计算属性
    canUseWechatFeatures,
    isWechatUser,
    
    // 方法
    detectWechatEnv,
    initWechatSDK,
    getWechatUserInfo,
    configShare,
    wechatPay,
    getLocation,
    scanQRCode,
    chooseImage,
    clearWechatData
  }
}, {
  // 持久化配置
  persist: {
    key: 'wechat-store',
    paths: ['userInfo', 'shareConfig']
  }
})
```

## 📍 地理位置状态管理

### 位置Store定义
```javascript
// stores/modules/location.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { locationApi } from '@/api/location'
import { useWechatStore } from './wechat'

export const useLocationStore = defineStore('location', () => {
  const wechatStore = useWechatStore()
  
  // 状态定义
  const currentLocation = ref({})
  const locationPermission = ref('prompt') // 'granted', 'denied', 'prompt'
  const nearbyStores = ref([])
  const selectedAddress = ref({})
  const addressList = ref([])
  const loading = ref(false)
  
  // 计算属性
  const hasLocation = computed(() => 
    currentLocation.value.latitude && currentLocation.value.longitude
  )
  
  const locationText = computed(() => {
    if (currentLocation.value.address) {
      return currentLocation.value.address
    }
    if (hasLocation.value) {
      return `${currentLocation.value.latitude}, ${currentLocation.value.longitude}`
    }
    return '未获取位置'
  })
  
  // 检查位置权限
  const checkLocationPermission = async () => {
    if ('geolocation' in navigator && 'permissions' in navigator) {
      try {
        const permission = await navigator.permissions.query({ name: 'geolocation' })
        locationPermission.value = permission.state
        return permission.state
      } catch (error) {
        console.warn('无法检查位置权限:', error)
        return 'prompt'
      }
    }
    return 'prompt'
  }
  
  // 获取当前位置
  const getCurrentLocation = async (useWechat = true) => {
    loading.value = true
    
    try {
      let location = null
      
      // 优先使用微信定位
      if (useWechat && wechatStore.canUseWechatFeatures) {
        try {
          location = await wechatStore.getLocation()
        } catch (error) {
          console.warn('微信定位失败，尝试浏览器定位:', error)
        }
      }
      
      // 使用浏览器定位
      if (!location) {
        location = await getBrowserLocation()
      }
      
      if (location) {
        // 获取地址信息
        const address = await getAddressByLocation(location.latitude, location.longitude)
        location.address = address
        
        currentLocation.value = location
        
        // 获取附近商家
        await getNearbyStores(location.latitude, location.longitude)
        
        return location
      } else {
        throw new Error('无法获取位置信息')
      }
    } catch (error) {
      console.error('获取位置失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }
  
  // 浏览器定位
  const getBrowserLocation = () => {
    return new Promise((resolve, reject) => {
      if (!('geolocation' in navigator)) {
        reject(new Error('浏览器不支持定位功能'))
        return
      }
      
      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy
          })
        },
        (error) => {
          let message = '定位失败'
          switch (error.code) {
            case error.PERMISSION_DENIED:
              message = '用户拒绝了定位请求'
              locationPermission.value = 'denied'
              break
            case error.POSITION_UNAVAILABLE:
              message = '位置信息不可用'
              break
            case error.TIMEOUT:
              message = '定位请求超时'
              break
          }
          reject(new Error(message))
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 300000 // 5分钟缓存
        }
      )
    })
  }
  
  // 根据坐标获取地址
  const getAddressByLocation = async (latitude, longitude) => {
    try {
      const response = await locationApi.getAddressByLocation({
        latitude,
        longitude
      })
      
      if (response.success) {
        return response.result.address
      } else {
        throw new Error(response.message)
      }
    } catch (error) {
      console.error('获取地址失败:', error)
      return `${latitude}, ${longitude}`
    }
  }
  
  // 获取附近商家
  const getNearbyStores = async (latitude, longitude, radius = 5000) => {
    try {
      const response = await locationApi.getNearbyStores({
        latitude,
        longitude,
        radius
      })
      
      if (response.success) {
        nearbyStores.value = response.result
        return response.result
      }
    } catch (error) {
      console.error('获取附近商家失败:', error)
    }
  }
  
  // 搜索地址
  const searchAddress = async (keyword) => {
    try {
      const response = await locationApi.searchAddress({
        keyword,
        location: hasLocation.value ? 
          `${currentLocation.value.latitude},${currentLocation.value.longitude}` : 
          undefined
      })
      
      if (response.success) {
        return response.result
      } else {
        throw new Error(response.message || '搜索地址失败')
      }
    } catch (error) {
      console.error('搜索地址失败:', error)
      throw error
    }
  }
  
  // 获取用户地址列表
  const getAddressList = async () => {
    try {
      const response = await locationApi.getUserAddressList()
      if (response.success) {
        addressList.value = response.result
        return response.result
      }
    } catch (error) {
      console.error('获取地址列表失败:', error)
    }
  }
  
  // 添加用户地址
  const addAddress = async (addressData) => {
    try {
      const response = await locationApi.addUserAddress(addressData)
      if (response.success) {
        // 更新本地地址列表
        await getAddressList()
        return response.result
      } else {
        throw new Error(response.message || '添加地址失败')
      }
    } catch (error) {
      console.error('添加地址失败:', error)
      throw error
    }
  }
  
  // 更新用户地址
  const updateAddress = async (addressId, addressData) => {
    try {
      const response = await locationApi.updateUserAddress(addressId, addressData)
      if (response.success) {
        // 更新本地地址列表
        await getAddressList()
        return response.result
      } else {
        throw new Error(response.message || '更新地址失败')
      }
    } catch (error) {
      console.error('更新地址失败:', error)
      throw error
    }
  }
  
  // 删除用户地址
  const deleteAddress = async (addressId) => {
    try {
      const response = await locationApi.deleteUserAddress(addressId)
      if (response.success) {
        // 更新本地地址列表
        await getAddressList()
        return response.result
      } else {
        throw new Error(response.message || '删除地址失败')
      }
    } catch (error) {
      console.error('删除地址失败:', error)
      throw error
    }
  }
  
  // 设置选中地址
  const setSelectedAddress = (address) => {
    selectedAddress.value = address
  }
  
  // 清除位置数据
  const clearLocationData = () => {
    currentLocation.value = {}
    nearbyStores.value = []
    selectedAddress.value = {}
  }
  
  return {
    // 状态
    currentLocation,
    locationPermission,
    nearbyStores,
    selectedAddress,
    addressList,
    loading,
    
    // 计算属性
    hasLocation,
    locationText,
    
    // 方法
    checkLocationPermission,
    getCurrentLocation,
    getBrowserLocation,
    getAddressByLocation,
    getNearbyStores,
    searchAddress,
    getAddressList,
    addAddress,
    updateAddress,
    deleteAddress,
    setSelectedAddress,
    clearLocationData
  }
}, {
  // 持久化配置
  persist: {
    key: 'location-store',
    paths: ['selectedAddress', 'addressList']
  }
})
```

## 📱 设备信息状态管理

### 设备Store定义
```javascript
// stores/modules/device.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useDeviceStore = defineStore('device', () => {
  // 状态定义
  const screenWidth = ref(0)
  const screenHeight = ref(0)
  const devicePixelRatio = ref(1)
  const userAgent = ref('')
  const platform = ref('')
  const networkType = ref('unknown')
  const isOnline = ref(true)
  const orientation = ref('portrait')
  
  // 计算属性
  const isMobile = computed(() => screenWidth.value < 768)
  const isTablet = computed(() => screenWidth.value >= 768 && screenWidth.value < 1024)
  const isDesktop = computed(() => screenWidth.value >= 1024)
  
  const deviceType = computed(() => {
    if (isMobile.value) return 'mobile'
    if (isTablet.value) return 'tablet'
    return 'desktop'
  })
  
  const isRetina = computed(() => devicePixelRatio.value >= 2)
  
  const isLandscape = computed(() => orientation.value === 'landscape')
  const isPortrait = computed(() => orientation.value === 'portrait')
  
  // 初始化设备信息
  const initDeviceInfo = () => {
    // 屏幕信息
    updateScreenInfo()
    
    // 设备信息
    devicePixelRatio.value = window.devicePixelRatio || 1
    userAgent.value = navigator.userAgent
    platform.value = navigator.platform
    
    // 网络状态
    updateNetworkStatus()
    
    // 监听事件
    window.addEventListener('resize', updateScreenInfo)
    window.addEventListener('orientationchange', updateOrientation)
    window.addEventListener('online', () => { isOnline.value = true })
    window.addEventListener('offline', () => { isOnline.value = false })
    
    // 监听网络变化
    if ('connection' in navigator) {
      const connection = navigator.connection
      networkType.value = connection.effectiveType || connection.type || 'unknown'
      
      connection.addEventListener('change', updateNetworkStatus)
    }
  }
  
  // 更新屏幕信息
  const updateScreenInfo = () => {
    screenWidth.value = window.innerWidth
    screenHeight.value = window.innerHeight
    
    // 更新方向
    if (screenWidth.value > screenHeight.value) {
      orientation.value = 'landscape'
    } else {
      orientation.value = 'portrait'
    }
  }
  
  // 更新方向
  const updateOrientation = () => {
    setTimeout(() => {
      updateScreenInfo()
    }, 100) // 延迟获取，确保尺寸更新完成
  }
  
  // 更新网络状态
  const updateNetworkStatus = () => {
    isOnline.value = navigator.onLine
    
    if ('connection' in navigator) {
      const connection = navigator.connection
      networkType.value = connection.effectiveType || connection.type || 'unknown'
    }
  }
  
  // 获取设备详细信息
  const getDeviceDetails = () => {
    return {
      screen: {
        width: screenWidth.value,
        height: screenHeight.value,
        pixelRatio: devicePixelRatio.value,
        orientation: orientation.value
      },
      device: {
        type: deviceType.value,
        userAgent: userAgent.value,
        platform: platform.value,
        isRetina: isRetina.value
      },
      network: {
        type: networkType.value,
        isOnline: isOnline.value
      }
    }
  }
  
  // 检测特定设备
  const detectDevice = () => {
    const ua = userAgent.value.toLowerCase()
    
    return {
      isIOS: /iphone|ipad|ipod/.test(ua),
      isAndroid: /android/.test(ua),
      isWechat: /micromessenger/.test(ua),
      isWeibo: /weibo/.test(ua),
      isQQ: /qq\//.test(ua),
      isSafari: /safari/.test(ua) && !/chrome/.test(ua),
      isChrome: /chrome/.test(ua),
      isFirefox: /firefox/.test(ua)
    }
  }
  
  // 销毁监听器
  const destroyListeners = () => {
    window.removeEventListener('resize', updateScreenInfo)
    window.removeEventListener('orientationchange', updateOrientation)
    
    if ('connection' in navigator) {
      navigator.connection.removeEventListener('change', updateNetworkStatus)
    }
  }
  
  return {
    // 状态
    screenWidth,
    screenHeight,
    devicePixelRatio,
    userAgent,
    platform,
    networkType,
    isOnline,
    orientation,
    
    // 计算属性
    isMobile,
    isTablet,
    isDesktop,
    deviceType,
    isRetina,
    isLandscape,
    isPortrait,
    
    // 方法
    initDeviceInfo,
    updateScreenInfo,
    updateOrientation,
    updateNetworkStatus,
    getDeviceDetails,
    detectDevice,
    destroyListeners
  }
})
```

## 📱 在组件中使用Store

### H5端组件使用示例
```vue
<template>
  <view class="h5-page" :class="deviceClass">
    <!-- 微信功能区域 -->
    <view class="wechat-section" v-if="wechatStore.isWechatEnv">
      <button @click="handleShare" v-if="wechatStore.canUseWechatFeatures">
        分享给好友
      </button>
      <button @click="handleLocation" :loading="locationStore.loading">
        获取位置
      </button>
    </view>
    
    <!-- 位置信息 -->
    <view class="location-info" v-if="locationStore.hasLocation">
      <text>当前位置: {{ locationStore.locationText }}</text>
    </view>
    
    <!-- 设备信息 -->
    <view class="device-info">
      <text>设备类型: {{ deviceStore.deviceType }}</text>
      <text>网络状态: {{ deviceStore.networkType }}</text>
    </view>
  </view>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from 'vue'
import { useWechatStore } from '@/stores/wechat'
import { useLocationStore } from '@/stores/location'
import { useDeviceStore } from '@/stores/device'

const wechatStore = useWechatStore()
const locationStore = useLocationStore()
const deviceStore = useDeviceStore()

// 计算属性
const deviceClass = computed(() => ({
  'device-mobile': deviceStore.isMobile,
  'device-tablet': deviceStore.isTablet,
  'device-desktop': deviceStore.isDesktop,
  'orientation-landscape': deviceStore.isLandscape
}))

// 事件处理
const handleShare = () => {
  wechatStore.configShare({
    title: '八闽助业集市',
    desc: '发现优质商品，享受便捷购物',
    link: window.location.href,
    imgUrl: 'https://example.com/share-icon.png'
  })
}

const handleLocation = async () => {
  try {
    await locationStore.getCurrentLocation()
  } catch (error) {
    console.error('获取位置失败:', error)
  }
}

// 生命周期
onMounted(async () => {
  // 初始化设备信息
  deviceStore.initDeviceInfo()
  
  // 检测微信环境并初始化
  wechatStore.detectWechatEnv()
  if (wechatStore.isWechatEnv) {
    await wechatStore.initWechatSDK()
  }
  
  // 检查位置权限
  await locationStore.checkLocationPermission()
})

onUnmounted(() => {
  deviceStore.destroyListeners()
})
</script>
```

## 📋 H5端状态管理最佳实践

### 1. 环境检测
- 准确检测微信和企业微信环境
- 处理不同浏览器的兼容性问题
- 优雅降级非微信环境功能

### 2. 权限管理
- 合理请求位置权限
- 处理权限被拒绝的情况
- 提供权限说明和引导

### 3. 网络处理
- 监听网络状态变化
- 处理离线场景
- 实现网络重连机制

### 4. 性能优化
- 合理使用localStorage
- 避免频繁的DOM操作
- 使用防抖和节流优化

### 5. 错误处理
- 统一的错误处理机制
- 友好的用户提示
- 错误日志收集和上报

---

**文档说明**: 本文档为八闽助业集市H5端Pinia状态管理规范，专注于微信公众号和H5环境的状态管理设计和最佳实践。
