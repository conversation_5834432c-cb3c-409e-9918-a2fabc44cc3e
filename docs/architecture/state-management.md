# 八闽助业集市C端小程序状态管理规范

## 🏗️ Pinia状态管理架构

### Pinia vs Vuex对比
```
┌─────────────────────────────────────────────────────────────────┐
│                        Pinia优势                                │
├─────────────────────────────────────────────────────────────────┤
│  TypeScript支持  → 完整的类型推断和类型安全                      │
│  组合式API      → 与Vue3 Composition API完美集成               │
│  模块化设计     → 自动代码分割，按需加载                         │
│  开发工具       → 更好的DevTools支持                           │
│  简化语法       → 无需mutations，直接修改state                  │
└─────────────────────────────────────────────────────────────────┘
```

### Store模块划分
```javascript
// stores/index.js
import { createPinia } from 'pinia'
import { createPersistedState } from 'pinia-plugin-persistedstate'

const pinia = createPinia()

// 持久化插件配置
pinia.use(
  createPersistedState({
    storage: {
      getItem: (key) => uni.getStorageSync(key),
      setItem: (key, value) => uni.setStorageSync(key, value),
      removeItem: (key) => uni.removeStorageSync(key)
    }
  })
)

export default pinia

// 导出所有store
export { useUserStore } from './modules/user'
export { useCartStore } from './modules/cart'
export { useProductStore } from './modules/product'
export { useOrderStore } from './modules/order'
export { useAppStore } from './modules/app'
```

## 👤 用户状态管理

### 用户Store定义
```javascript
// stores/modules/user.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userApi } from '@/api/user'
import { loginApi } from '@/api/login'

export const useUserStore = defineStore('user', () => {
  // 状态定义
  const token = ref('')
  const userInfo = ref({})
  const isLogin = computed(() => !!token.value)
  const avatar = computed(() => userInfo.value.avatar || '/static/images/default-avatar.png')
  const nickname = computed(() => userInfo.value.nickname || '未设置昵称')
  
  // 登录
  const login = async (loginData) => {
    try {
      const response = await loginApi.login(loginData)
      if (response.success) {
        token.value = response.result.token
        userInfo.value = response.result.userInfo
        
        // 登录成功提示
        uni.showToast({
          title: '登录成功',
          icon: 'success'
        })
        
        return response.result
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '登录失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 微信登录
  const wechatLogin = async () => {
    try {
      // 获取微信授权
      const loginRes = await uni.login({
        provider: 'weixin'
      })
      
      if (loginRes.errMsg === 'login:ok') {
        const response = await loginApi.wechatLogin({
          code: loginRes.code
        })
        
        if (response.success) {
          token.value = response.result.token
          userInfo.value = response.result.userInfo
          
          uni.showToast({
            title: '登录成功',
            icon: 'success'
          })
          
          return response.result
        } else {
          throw new Error(response.message || '微信登录失败')
        }
      } else {
        throw new Error('微信授权失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '微信登录失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 获取用户信息
  const getUserInfo = async () => {
    try {
      const response = await userApi.getUserInfo()
      if (response.success) {
        userInfo.value = response.result
        return response.result
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
    }
  }
  
  // 更新用户信息
  const updateUserInfo = async (data) => {
    try {
      const response = await userApi.updateUserInfo(data)
      if (response.success) {
        userInfo.value = { ...userInfo.value, ...data }
        uni.showToast({
          title: '更新成功',
          icon: 'success'
        })
        return response.result
      } else {
        throw new Error(response.message || '更新失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '更新失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 退出登录
  const logout = async () => {
    try {
      if (token.value) {
        await loginApi.logout()
      }
    } catch (error) {
      console.error('退出登录失败:', error)
    } finally {
      // 清除本地数据
      token.value = ''
      userInfo.value = {}
      
      // 清除其他store的用户相关数据
      const cartStore = useCartStore()
      cartStore.clearCart()
      
      uni.showToast({
        title: '已退出登录',
        icon: 'success'
      })
      
      // 跳转到登录页
      uni.reLaunch({
        url: '/pages/user/login'
      })
    }
  }
  
  // 收藏/取消收藏
  const toggleFavorite = async (productId) => {
    try {
      const response = await userApi.toggleFavorite(productId)
      if (response.success) {
        return response.result
      } else {
        throw new Error(response.message || '操作失败')
      }
    } catch (error) {
      throw error
    }
  }
  
  // 获取收藏列表
  const getFavoriteList = async (params = {}) => {
    try {
      const response = await userApi.getFavoriteList(params)
      if (response.success) {
        return response.result
      } else {
        throw new Error(response.message || '获取收藏列表失败')
      }
    } catch (error) {
      throw error
    }
  }
  
  return {
    // 状态
    token,
    userInfo,
    isLogin,
    avatar,
    nickname,
    
    // 方法
    login,
    wechatLogin,
    getUserInfo,
    updateUserInfo,
    logout,
    toggleFavorite,
    getFavoriteList
  }
}, {
  // 持久化配置
  persist: {
    key: 'user-store',
    storage: {
      getItem: (key) => uni.getStorageSync(key),
      setItem: (key, value) => uni.setStorageSync(key, value),
      removeItem: (key) => uni.removeStorageSync(key)
    },
    paths: ['token', 'userInfo']
  }
})
```

## 🛒 购物车状态管理

### 购物车Store定义
```javascript
// stores/modules/cart.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { cartApi } from '@/api/cart'
import { useUserStore } from './user'

export const useCartStore = defineStore('cart', () => {
  const userStore = useUserStore()
  
  // 状态定义
  const cartItems = ref([])
  const loading = ref(false)
  
  // 计算属性
  const cartCount = computed(() => {
    return cartItems.value.reduce((total, item) => total + item.quantity, 0)
  })
  
  const selectedItems = computed(() => {
    return cartItems.value.filter(item => item.selected)
  })
  
  const selectedCount = computed(() => {
    return selectedItems.value.reduce((total, item) => total + item.quantity, 0)
  })
  
  const totalPrice = computed(() => {
    return selectedItems.value.reduce((total, item) => {
      return total + (item.price * item.quantity)
    }, 0)
  })
  
  const allSelected = computed(() => {
    return cartItems.value.length > 0 && cartItems.value.every(item => item.selected)
  })
  
  // 获取购物车列表
  const getCartList = async () => {
    if (!userStore.isLogin) return
    
    try {
      loading.value = true
      const response = await cartApi.getCartList()
      if (response.success) {
        cartItems.value = response.result.map(item => ({
          ...item,
          selected: true
        }))
      }
    } catch (error) {
      console.error('获取购物车失败:', error)
    } finally {
      loading.value = false
    }
  }
  
  // 添加到购物车
  const addToCart = async (productId, skuId, quantity = 1) => {
    if (!userStore.isLogin) {
      uni.navigateTo({
        url: '/pages/user/login'
      })
      return
    }
    
    try {
      const response = await cartApi.addToCart({
        productId,
        skuId,
        quantity
      })
      
      if (response.success) {
        // 更新本地购物车
        await getCartList()
        
        uni.showToast({
          title: '已添加到购物车',
          icon: 'success'
        })
        
        return response.result
      } else {
        throw new Error(response.message || '添加失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '添加失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 更新购物车商品数量
  const updateCartItem = async (cartId, quantity) => {
    try {
      const response = await cartApi.updateCartItem(cartId, { quantity })
      if (response.success) {
        // 更新本地数据
        const item = cartItems.value.find(item => item.id === cartId)
        if (item) {
          item.quantity = quantity
        }
      } else {
        throw new Error(response.message || '更新失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '更新失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 删除购物车商品
  const removeCartItem = async (cartId) => {
    try {
      const response = await cartApi.removeCartItem(cartId)
      if (response.success) {
        // 更新本地数据
        const index = cartItems.value.findIndex(item => item.id === cartId)
        if (index > -1) {
          cartItems.value.splice(index, 1)
        }
        
        uni.showToast({
          title: '已删除',
          icon: 'success'
        })
      } else {
        throw new Error(response.message || '删除失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '删除失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 批量删除购物车商品
  const batchRemoveCartItems = async (cartIds) => {
    try {
      const response = await cartApi.batchRemoveCartItems(cartIds)
      if (response.success) {
        // 更新本地数据
        cartItems.value = cartItems.value.filter(item => !cartIds.includes(item.id))
        
        uni.showToast({
          title: '已删除',
          icon: 'success'
        })
      } else {
        throw new Error(response.message || '删除失败')
      }
    } catch (error) {
      uni.showToast({
        title: error.message || '删除失败',
        icon: 'error'
      })
      throw error
    }
  }
  
  // 选择/取消选择商品
  const toggleSelectItem = (cartId) => {
    const item = cartItems.value.find(item => item.id === cartId)
    if (item) {
      item.selected = !item.selected
    }
  }
  
  // 全选/取消全选
  const toggleSelectAll = () => {
    const selectAll = !allSelected.value
    cartItems.value.forEach(item => {
      item.selected = selectAll
    })
  }
  
  // 清空购物车
  const clearCart = () => {
    cartItems.value = []
  }
  
  return {
    // 状态
    cartItems,
    loading,
    
    // 计算属性
    cartCount,
    selectedItems,
    selectedCount,
    totalPrice,
    allSelected,
    
    // 方法
    getCartList,
    addToCart,
    updateCartItem,
    removeCartItem,
    batchRemoveCartItems,
    toggleSelectItem,
    toggleSelectAll,
    clearCart
  }
}, {
  // 持久化配置
  persist: {
    key: 'cart-store',
    storage: {
      getItem: (key) => uni.getStorageSync(key),
      setItem: (key, value) => uni.setStorageSync(key, value),
      removeItem: (key) => uni.removeStorageSync(key)
    },
    paths: ['cartItems']
  }
})
```

## 🏪 应用全局状态管理

### 应用Store定义
```javascript
// stores/modules/app.js
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useAppStore = defineStore('app', () => {
  // 系统信息
  const systemInfo = ref({})
  const statusBarHeight = ref(0)
  const navigationBarHeight = ref(44)
  const tabBarHeight = ref(50)
  
  // 网络状态
  const networkType = ref('wifi')
  const isOnline = ref(true)
  
  // 主题配置
  const theme = ref('light')
  const primaryColor = ref('#667eea')
  
  // 计算属性
  const isDark = computed(() => theme.value === 'dark')
  const safeAreaTop = computed(() => statusBarHeight.value)
  const safeAreaBottom = computed(() => systemInfo.value.safeAreaInsets?.bottom || 0)
  
  // 初始化系统信息
  const initSystemInfo = () => {
    const info = uni.getSystemInfoSync()
    systemInfo.value = info
    statusBarHeight.value = info.statusBarHeight || 0
    
    // 计算导航栏高度
    if (info.platform === 'ios') {
      navigationBarHeight.value = 44
    } else {
      navigationBarHeight.value = 48
    }
  }
  
  // 监听网络状态
  const initNetworkStatus = () => {
    uni.getNetworkType({
      success: (res) => {
        networkType.value = res.networkType
        isOnline.value = res.networkType !== 'none'
      }
    })
    
    uni.onNetworkStatusChange((res) => {
      networkType.value = res.networkType
      isOnline.value = res.isConnected
      
      if (!res.isConnected) {
        uni.showToast({
          title: '网络连接已断开',
          icon: 'error'
        })
      }
    })
  }
  
  // 设置主题
  const setTheme = (newTheme) => {
    theme.value = newTheme
    
    // 设置系统状态栏样式
    uni.setNavigationBarColor({
      frontColor: newTheme === 'dark' ? '#ffffff' : '#000000',
      backgroundColor: newTheme === 'dark' ? '#1f1f1f' : '#ffffff'
    })
  }
  
  // 设置主色调
  const setPrimaryColor = (color) => {
    primaryColor.value = color
  }
  
  // 显示加载提示
  const showLoading = (title = '加载中...') => {
    uni.showLoading({
      title,
      mask: true
    })
  }
  
  // 隐藏加载提示
  const hideLoading = () => {
    uni.hideLoading()
  }
  
  // 显示消息提示
  const showToast = (options) => {
    if (typeof options === 'string') {
      uni.showToast({
        title: options,
        icon: 'none'
      })
    } else {
      uni.showToast({
        icon: 'none',
        ...options
      })
    }
  }
  
  // 显示确认对话框
  const showConfirm = (options) => {
    return new Promise((resolve) => {
      uni.showModal({
        title: '提示',
        ...options,
        success: (res) => {
          resolve(res.confirm)
        },
        fail: () => {
          resolve(false)
        }
      })
    })
  }
  
  return {
    // 状态
    systemInfo,
    statusBarHeight,
    navigationBarHeight,
    tabBarHeight,
    networkType,
    isOnline,
    theme,
    primaryColor,
    
    // 计算属性
    isDark,
    safeAreaTop,
    safeAreaBottom,
    
    // 方法
    initSystemInfo,
    initNetworkStatus,
    setTheme,
    setPrimaryColor,
    showLoading,
    hideLoading,
    showToast,
    showConfirm
  }
}, {
  // 持久化配置
  persist: {
    key: 'app-store',
    storage: {
      getItem: (key) => uni.getStorageSync(key),
      setItem: (key, value) => uni.setStorageSync(key, value),
      removeItem: (key) => uni.removeStorageSync(key)
    },
    paths: ['theme', 'primaryColor']
  }
})
```

## 📱 在组件中使用Store

### 组件中使用示例
```vue
<template>
  <view class="user-profile">
    <view class="user-info" v-if="userStore.isLogin">
      <image :src="userStore.avatar" class="avatar" />
      <text class="nickname">{{ userStore.nickname }}</text>
      <text class="logout-btn" @click="handleLogout">退出登录</text>
    </view>
    
    <view class="login-prompt" v-else>
      <text @click="handleLogin">点击登录</text>
    </view>
    
    <view class="cart-info">
      <text>购物车商品数量: {{ cartStore.cartCount }}</text>
      <text>选中商品总价: ¥{{ cartStore.totalPrice.toFixed(2) }}</text>
    </view>
  </view>
</template>

<script setup>
import { useUserStore } from '@/stores/user'
import { useCartStore } from '@/stores/cart'
import { useAppStore } from '@/stores/app'

const userStore = useUserStore()
const cartStore = useCartStore()
const appStore = useAppStore()

// 登录
const handleLogin = () => {
  uni.navigateTo({
    url: '/pages/user/login'
  })
}

// 退出登录
const handleLogout = async () => {
  const confirm = await appStore.showConfirm({
    title: '确认退出',
    content: '确定要退出登录吗？'
  })
  
  if (confirm) {
    await userStore.logout()
  }
}

// 页面加载时获取购物车数据
onMounted(() => {
  if (userStore.isLogin) {
    cartStore.getCartList()
  }
})
</script>
```

## 📋 状态管理最佳实践

### 1. Store设计原则
- 按功能模块划分Store
- 使用组合式API风格
- 合理使用计算属性
- 避免在Store中存储临时状态

### 2. 持久化策略
- 只持久化必要的状态
- 敏感信息加密存储
- 定期清理过期数据
- 处理存储异常情况

### 3. 性能优化
- 避免频繁的状态更新
- 使用computed缓存计算结果
- 合理使用响应式数据
- 避免在模板中直接调用方法

### 4. 错误处理
- 统一的错误处理机制
- 友好的用户提示
- 记录错误日志
- 提供重试机制

### 5. 类型安全
- 使用TypeScript定义类型
- 为Store提供类型推断
- 避免any类型的使用
- 提供完整的类型注释

---

**文档说明**: 本文档为八闽助业集市C端小程序Pinia状态管理规范，基于Vue3 Composition API，所有状态管理必须严格遵循此规范。
