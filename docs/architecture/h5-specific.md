# H5端特定功能架构文档

## 概述

八闽助业集市H5端是专门为微信公众号环境设计的Web应用，具有独特的功能特性和技术实现方案。

## H5端技术架构

### 核心技术栈
```json
{
  "framework": "UniApp + Vue 3",
  "composition": "Composition API",
  "platform": "H5 (微信公众号)",
  "wechat": "微信JS SDK 1.6.0",
  "enterprise": "企业微信SDK",
  "http": "luch-request",
  "state": "Pinia",
  "build": "HBuilderX + Vite"
}
```

### 项目结构特点
```
heartful-mall-h5/
├── manifest.json                       # H5专用配置
│   ├── h5: {                          # H5平台配置
│   │   "title": "八闽助业集市",
│   │   "template": "index.html",
│   │   "router": {
│   │     "mode": "hash"               # 微信环境使用hash模式
│   │   },
│   │   "optimization": {
│   │     "treeShaking": {
│   │       "enable": true
│   │     }
│   │   }
│   └── }
├── pages.json                         # H5页面配置
└── utils/
    ├── wechat.js                      # 微信SDK工具
    ├── enterprise.js                  # 企业微信工具
    ├── h5-adapter.js                  # H5适配工具
    └── environment.js                 # 环境检测工具
```

## H5特定功能实现

### 1. 微信环境适配

#### 环境检测与初始化
```javascript
// utils/h5-init.js
import { EnvironmentDetector } from './environment.js'
import WechatSDK from './wechat.js'
import { H5Adapter } from './h5-adapter.js'

export class H5Initializer {
  static async init() {
    console.log('H5应用初始化开始')
    
    // 1. 环境检测
    const envType = EnvironmentDetector.getEnvironmentType()
    console.log('当前环境:', envType)
    
    // 2. 设置基础适配
    H5Adapter.disablePullRefresh()
    H5Adapter.setStatusBarStyle('dark')
    
    // 3. 微信环境特殊处理
    if (envType === 'wechat' || envType === 'work-wechat') {
      await this.initWechatEnvironment()
    }
    
    // 4. 设置全局错误处理
    this.setupErrorHandling()
    
    // 5. 初始化性能监控
    this.initPerformanceMonitor()
    
    console.log('H5应用初始化完成')
  }

  // 初始化微信环境
  static async initWechatEnvironment() {
    try {
      // 初始化微信SDK
      await WechatSDK.init()
      
      // 设置默认分享内容
      await this.setupDefaultShare()
      
      // 隐藏微信右上角菜单
      this.hideWechatMenuItems()
      
    } catch (error) {
      console.error('微信环境初始化失败:', error)
    }
  }

  // 设置默认分享
  static async setupDefaultShare() {
    const { WechatShare } = await import('./wechat-share.js')
    await WechatShare.configShare({
      title: '八闽助业集市',
      desc: '福建省内优质商品，助力乡村振兴',
      link: window.location.href,
      imgUrl: `${window.location.origin}/static/images/share-logo.png`
    })
  }

  // 隐藏微信菜单项
  static hideWechatMenuItems() {
    if (typeof wx !== 'undefined') {
      wx.hideMenuItems({
        menuList: [
          'menuItem:copyUrl',
          'menuItem:openWithQQBrowser',
          'menuItem:openWithSafari'
        ]
      })
    }
  }

  // 设置错误处理
  static setupErrorHandling() {
    const { H5ErrorHandler } = require('./h5-error-handler.js')
    H5ErrorHandler.init()
  }

  // 初始化性能监控
  static initPerformanceMonitor() {
    // 页面加载时间监控
    window.addEventListener('load', () => {
      const loadTime = performance.now()
      console.log('页面加载时间:', loadTime + 'ms')
      
      // 上报性能数据
      this.reportPerformance({
        type: 'page-load',
        duration: loadTime,
        url: window.location.href
      })
    })
  }

  // 性能数据上报
  static async reportPerformance(data) {
    try {
      await uni.request({
        url: '/front/performance/report',
        method: 'POST',
        data: {
          ...data,
          timestamp: Date.now(),
          userAgent: navigator.userAgent
        }
      })
    } catch (error) {
      console.error('性能数据上报失败:', error)
    }
  }
}
```

### 2. 页面路由管理

#### Hash路由配置
```javascript
// utils/h5-router.js
export class H5Router {
  constructor() {
    this.routes = new Map()
    this.currentRoute = ''
    this.init()
  }

  init() {
    // 监听hash变化
    window.addEventListener('hashchange', this.handleHashChange.bind(this))
    
    // 处理初始路由
    this.handleHashChange()
  }

  // 处理路由变化
  handleHashChange() {
    const hash = window.location.hash.slice(1) || '/'
    const [path, query] = hash.split('?')
    
    this.currentRoute = path
    this.handleRouteChange(path, this.parseQuery(query))
  }

  // 解析查询参数
  parseQuery(queryString) {
    if (!queryString) return {}
    
    const params = {}
    queryString.split('&').forEach(param => {
      const [key, value] = param.split('=')
      if (key) {
        params[decodeURIComponent(key)] = decodeURIComponent(value || '')
      }
    })
    return params
  }

  // 处理路由变化
  handleRouteChange(path, query) {
    console.log('路由变化:', path, query)
    
    // 设置页面标题
    this.setPageTitle(path)
    
    // 更新分享内容
    this.updateShareContent(path, query)
    
    // 页面埋点
    this.trackPageView(path, query)
  }

  // 设置页面标题
  setPageTitle(path) {
    const titleMap = {
      '/': '八闽助业集市',
      '/goods/list': '商品列表',
      '/goods/detail': '商品详情',
      '/store/detail': '店铺详情',
      '/user/profile': '个人中心',
      '/user/orders': '我的订单'
    }
    
    const title = titleMap[path] || '八闽助业集市'
    H5Adapter.setTitle(title)
  }

  // 更新分享内容
  async updateShareContent(path, query) {
    const { WechatShare } = await import('./wechat-share.js')
    
    if (path === '/goods/detail' && query.id) {
      // 商品详情页分享
      const goodsInfo = await this.getGoodsInfo(query.id)
      if (goodsInfo) {
        await WechatShare.shareGoods(goodsInfo)
      }
    } else if (path === '/store/detail' && query.id) {
      // 店铺详情页分享
      const storeInfo = await this.getStoreInfo(query.id)
      if (storeInfo) {
        await WechatShare.shareStore(storeInfo)
      }
    }
  }

  // 页面访问统计
  async trackPageView(path, query) {
    try {
      await uni.request({
        url: '/front/analytics/page-view',
        method: 'POST',
        data: {
          path,
          query,
          referrer: document.referrer,
          timestamp: Date.now(),
          userAgent: navigator.userAgent
        }
      })
    } catch (error) {
      console.error('页面统计失败:', error)
    }
  }

  // 获取商品信息
  async getGoodsInfo(goodsId) {
    try {
      const response = await uni.request({
        url: `/front/goods/detail/${goodsId}`,
        method: 'GET'
      })
      return response.data.success ? response.data.result : null
    } catch (error) {
      console.error('获取商品信息失败:', error)
      return null
    }
  }

  // 获取店铺信息
  async getStoreInfo(storeId) {
    try {
      const response = await uni.request({
        url: `/front/store/detail/${storeId}`,
        method: 'GET'
      })
      return response.data.success ? response.data.result : null
    } catch (error) {
      console.error('获取店铺信息失败:', error)
      return null
    }
  }
}

// 全局路由实例
export const h5Router = new H5Router()
```

### 3. 用户体验优化

#### 加载状态管理
```javascript
// utils/loading-manager.js
export class LoadingManager {
  constructor() {
    this.loadingCount = 0
    this.loadingElement = null
    this.init()
  }

  init() {
    // 创建加载指示器
    this.createLoadingElement()
  }

  // 创建加载元素
  createLoadingElement() {
    this.loadingElement = document.createElement('div')
    this.loadingElement.className = 'h5-loading-overlay'
    this.loadingElement.innerHTML = `
      <div class="loading-spinner">
        <div class="spinner"></div>
        <div class="loading-text">加载中...</div>
      </div>
    `
    
    // 添加样式
    const style = document.createElement('style')
    style.textContent = `
      .h5-loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
      }
      
      .h5-loading-overlay.show {
        opacity: 1;
        visibility: visible;
      }
      
      .loading-spinner {
        text-align: center;
      }
      
      .spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #f3f3f3;
        border-top: 3px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 10px;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: #666;
        font-size: 14px;
      }
    `
    document.head.appendChild(style)
    document.body.appendChild(this.loadingElement)
  }

  // 显示加载
  show(text = '加载中...') {
    this.loadingCount++
    
    const textElement = this.loadingElement.querySelector('.loading-text')
    if (textElement) {
      textElement.textContent = text
    }
    
    this.loadingElement.classList.add('show')
  }

  // 隐藏加载
  hide() {
    this.loadingCount = Math.max(0, this.loadingCount - 1)
    
    if (this.loadingCount === 0) {
      this.loadingElement.classList.remove('show')
    }
  }

  // 强制隐藏
  forceHide() {
    this.loadingCount = 0
    this.loadingElement.classList.remove('show')
  }
}

// 全局加载管理器
export const loadingManager = new LoadingManager()
```

#### 图片懒加载
```javascript
// utils/image-lazy-load.js
export class ImageLazyLoad {
  constructor() {
    this.observer = null
    this.init()
  }

  init() {
    // 检查浏览器支持
    if ('IntersectionObserver' in window) {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        {
          rootMargin: '50px 0px',
          threshold: 0.1
        }
      )
    } else {
      // 降级处理
      this.fallbackLazyLoad()
    }
  }

  // 处理图片进入视口
  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target
        this.loadImage(img)
        this.observer.unobserve(img)
      }
    })
  }

  // 加载图片
  loadImage(img) {
    const src = img.dataset.src
    if (src) {
      img.src = src
      img.classList.add('loaded')
      
      // 移除data-src属性
      delete img.dataset.src
    }
  }

  // 观察图片
  observe(img) {
    if (this.observer) {
      this.observer.observe(img)
    } else {
      // 降级处理：直接加载
      this.loadImage(img)
    }
  }

  // 降级处理
  fallbackLazyLoad() {
    const images = document.querySelectorAll('img[data-src]')
    images.forEach(img => {
      this.loadImage(img)
    })
  }

  // 批量处理页面图片
  processPageImages() {
    const images = document.querySelectorAll('img[data-src]')
    images.forEach(img => {
      this.observe(img)
    })
  }
}

// 全局图片懒加载实例
export const imageLazyLoad = new ImageLazyLoad()
```

### 4. 数据缓存策略

#### 智能缓存管理
```javascript
// utils/smart-cache.js
export class SmartCache {
  constructor() {
    this.cachePrefix = 'h5_cache_'
    this.maxCacheSize = 50 * 1024 * 1024 // 50MB
    this.init()
  }

  init() {
    // 定期清理过期缓存
    setInterval(() => {
      this.cleanExpiredCache()
    }, 5 * 60 * 1000) // 5分钟清理一次
  }

  // 设置缓存
  set(key, data, options = {}) {
    const {
      expireTime = 30 * 60 * 1000, // 默认30分钟
      priority = 'normal', // low, normal, high
      compress = false
    } = options

    const cacheData = {
      data: compress ? this.compress(data) : data,
      expireTime: Date.now() + expireTime,
      priority,
      compressed: compress,
      size: JSON.stringify(data).length,
      timestamp: Date.now()
    }

    try {
      localStorage.setItem(
        this.cachePrefix + key,
        JSON.stringify(cacheData)
      )
    } catch (error) {
      // 存储空间不足，清理低优先级缓存
      this.cleanLowPriorityCache()
      
      try {
        localStorage.setItem(
          this.cachePrefix + key,
          JSON.stringify(cacheData)
        )
      } catch (retryError) {
        console.error('缓存设置失败:', retryError)
      }
    }
  }

  // 获取缓存
  get(key) {
    try {
      const item = localStorage.getItem(this.cachePrefix + key)
      if (!item) return null

      const cacheData = JSON.parse(item)
      
      // 检查是否过期
      if (Date.now() > cacheData.expireTime) {
        localStorage.removeItem(this.cachePrefix + key)
        return null
      }

      // 解压缩数据
      return cacheData.compressed 
        ? this.decompress(cacheData.data)
        : cacheData.data
    } catch (error) {
      console.error('缓存获取失败:', error)
      return null
    }
  }

  // 删除缓存
  remove(key) {
    localStorage.removeItem(this.cachePrefix + key)
  }

  // 清理过期缓存
  cleanExpiredCache() {
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith(this.cachePrefix)) {
        try {
          const item = localStorage.getItem(key)
          if (item) {
            const cacheData = JSON.parse(item)
            if (Date.now() > cacheData.expireTime) {
              localStorage.removeItem(key)
            }
          }
        } catch (error) {
          // 数据损坏，直接删除
          localStorage.removeItem(key)
        }
      }
    })
  }

  // 清理低优先级缓存
  cleanLowPriorityCache() {
    const cacheItems = []
    const keys = Object.keys(localStorage)
    
    keys.forEach(key => {
      if (key.startsWith(this.cachePrefix)) {
        try {
          const item = localStorage.getItem(key)
          if (item) {
            const cacheData = JSON.parse(item)
            cacheItems.push({
              key,
              ...cacheData
            })
          }
        } catch (error) {
          localStorage.removeItem(key)
        }
      }
    })

    // 按优先级和时间排序
    cacheItems.sort((a, b) => {
      const priorityOrder = { low: 0, normal: 1, high: 2 }
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[a.priority] - priorityOrder[b.priority]
      }
      return a.timestamp - b.timestamp
    })

    // 删除前30%的缓存
    const deleteCount = Math.ceil(cacheItems.length * 0.3)
    for (let i = 0; i < deleteCount; i++) {
      localStorage.removeItem(cacheItems[i].key)
    }
  }

  // 压缩数据
  compress(data) {
    // 简单的JSON压缩（实际项目中可使用更高效的压缩算法）
    return JSON.stringify(data)
  }

  // 解压缩数据
  decompress(compressedData) {
    return JSON.parse(compressedData)
  }

  // 获取缓存统计信息
  getStats() {
    const keys = Object.keys(localStorage)
    let totalSize = 0
    let itemCount = 0
    
    keys.forEach(key => {
      if (key.startsWith(this.cachePrefix)) {
        const item = localStorage.getItem(key)
        if (item) {
          totalSize += item.length
          itemCount++
        }
      }
    })

    return {
      itemCount,
      totalSize,
      totalSizeMB: (totalSize / 1024 / 1024).toFixed(2)
    }
  }
}

// 全局缓存实例
export const smartCache = new SmartCache()
```

### 5. 性能监控

#### 性能指标收集
```javascript
// utils/performance-monitor.js
export class PerformanceMonitor {
  constructor() {
    this.metrics = {}
    this.init()
  }

  init() {
    // 页面加载性能监控
    this.monitorPageLoad()
    
    // 资源加载监控
    this.monitorResourceLoad()
    
    // 用户交互监控
    this.monitorUserInteraction()
    
    // 内存使用监控
    this.monitorMemoryUsage()
  }

  // 页面加载性能监控
  monitorPageLoad() {
    window.addEventListener('load', () => {
      setTimeout(() => {
        const navigation = performance.getEntriesByType('navigation')[0]
        
        this.metrics.pageLoad = {
          dns: navigation.domainLookupEnd - navigation.domainLookupStart,
          tcp: navigation.connectEnd - navigation.connectStart,
          request: navigation.responseStart - navigation.requestStart,
          response: navigation.responseEnd - navigation.responseStart,
          dom: navigation.domContentLoadedEventEnd - navigation.navigationStart,
          load: navigation.loadEventEnd - navigation.navigationStart,
          firstPaint: this.getFirstPaint(),
          firstContentfulPaint: this.getFirstContentfulPaint()
        }
        
        this.reportMetrics('page-load', this.metrics.pageLoad)
      }, 0)
    })
  }

  // 获取首次绘制时间
  getFirstPaint() {
    const paintEntries = performance.getEntriesByType('paint')
    const firstPaint = paintEntries.find(entry => entry.name === 'first-paint')
    return firstPaint ? firstPaint.startTime : 0
  }

  // 获取首次内容绘制时间
  getFirstContentfulPaint() {
    const paintEntries = performance.getEntriesByType('paint')
    const fcp = paintEntries.find(entry => entry.name === 'first-contentful-paint')
    return fcp ? fcp.startTime : 0
  }

  // 资源加载监控
  monitorResourceLoad() {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach(entry => {
        if (entry.entryType === 'resource') {
          this.recordResourceMetric(entry)
        }
      })
    })
    
    observer.observe({ entryTypes: ['resource'] })
  }

  // 记录资源指标
  recordResourceMetric(entry) {
    const resourceType = this.getResourceType(entry.name)
    
    if (!this.metrics.resources) {
      this.metrics.resources = {}
    }
    
    if (!this.metrics.resources[resourceType]) {
      this.metrics.resources[resourceType] = {
        count: 0,
        totalSize: 0,
        totalDuration: 0,
        errors: 0
      }
    }
    
    const resource = this.metrics.resources[resourceType]
    resource.count++
    resource.totalSize += entry.transferSize || 0
    resource.totalDuration += entry.duration
    
    if (entry.responseStatus >= 400) {
      resource.errors++
    }
  }

  // 获取资源类型
  getResourceType(url) {
    if (url.includes('.js')) return 'javascript'
    if (url.includes('.css')) return 'stylesheet'
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) return 'image'
    if (url.includes('/api/') || url.includes('.json')) return 'api'
    return 'other'
  }

  // 用户交互监控
  monitorUserInteraction() {
    let clickCount = 0
    let scrollCount = 0
    
    document.addEventListener('click', () => {
      clickCount++
    })
    
    document.addEventListener('scroll', () => {
      scrollCount++
    })
    
    // 每分钟上报一次交互数据
    setInterval(() => {
      if (clickCount > 0 || scrollCount > 0) {
        this.reportMetrics('user-interaction', {
          clicks: clickCount,
          scrolls: scrollCount,
          timestamp: Date.now()
        })
        
        clickCount = 0
        scrollCount = 0
      }
    }, 60000)
  }

  // 内存使用监控
  monitorMemoryUsage() {
    if ('memory' in performance) {
      setInterval(() => {
        const memory = performance.memory
        this.metrics.memory = {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize,
          limit: memory.jsHeapSizeLimit,
          usage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit * 100).toFixed(2)
        }
        
        // 内存使用超过80%时上报
        if (this.metrics.memory.usage > 80) {
          this.reportMetrics('memory-warning', this.metrics.memory)
        }
      }, 30000) // 30秒检查一次
    }
  }

  // 上报性能指标
  async reportMetrics(type, data) {
    try {
      await uni.request({
        url: '/front/performance/metrics',
        method: 'POST',
        data: {
          type,
          data,
          url: window.location.href,
          userAgent: navigator.userAgent,
          timestamp: Date.now()
        }
      })
    } catch (error) {
      console.error('性能指标上报失败:', error)
    }
  }

  // 获取当前性能指标
  getCurrentMetrics() {
    return this.metrics
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor()
```

## 部署配置

### Nginx配置
```nginx
# H5端专用Nginx配置
server {
    listen 80;
    server_name h5.heartful-mall.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name h5.heartful-mall.com;
    
    # SSL配置
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # 静态资源
    location / {
        root /var/www/heartful-mall-h5;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 缓存配置
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://backend-server;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 微信验证文件
    location ~ ^/(MP_verify_.*\.txt)$ {
        root /var/www/wechat-verify;
    }
}
```

本文档详细说明了H5端的特定功能实现，确保在微信公众号环境中的最佳用户体验和性能表现。
