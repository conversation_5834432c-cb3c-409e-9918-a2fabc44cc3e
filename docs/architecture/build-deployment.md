# 八闽助业集市商家端小程序构建部署规范

## 🏗️ 商家端构建配置

### 项目结构特点
```
┌─────────────────────────────────────────────────────────────────┐
│                    商家端项目结构                                │
├─────────────────────────────────────────────────────────────────┤
│  manifest.json   → 商家端专用应用配置                           │
│  pages.json      → 商家业务页面路由配置                         │
│  /pages-merchant → 商家专用页面目录                             │
│  /components     → 商家业务组件                                 │
│  /api           → 商家端API接口（/before/、/back/）             │
│  /stores        → 商家业务状态管理                              │
│  /utils         → 商家端工具函数                                │
└─────────────────────────────────────────────────────────────────┘
```

### manifest.json配置
```json
{
  "name": "八闽助业集市商家端",
  "appid": "__UNI__HEARTFUL_MERCHANT",
  "description": "八闽助业集市商家端小程序",
  "versionName": "1.0.0",
  "versionCode": "100",
  "transformPx": false,
  
  "app-plus": {
    "usingComponents": true,
    "nvueStyleCompiler": "uni-app",
    "compilerVersion": 3,
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": true,
      "autoclose": true,
      "delay": 0
    },
    "modules": {},
    "distribute": {
      "android": {
        "permissions": [
          "<uses-permission android:name=\"android.permission.CAMERA\" />",
          "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />",
          "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\" />",
          "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />",
          "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\" />",
          "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />",
          "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\" />",
          "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\" />"
        ]
      },
      "ios": {},
      "sdkConfigs": {
        "payment": {
          "weixin": {
            "appid": "wxMERCHANT_XXXXXXXX"
          },
          "alipay": {
            "scheme": "alipayMERCHANT_XXXXXXXX"
          }
        },
        "oauth": {
          "weixin": {
            "appid": "wxMERCHANT_XXXXXXXX",
            "appsecret": "MERCHANT_XXXXXXXXXXXXXXXX",
            "UniversalLinks": ""
          }
        }
      }
    }
  },
  
  "mp-weixin": {
    "appid": "wxMERCHANT_XXXXXXXX",
    "setting": {
      "urlCheck": false,
      "es6": true,
      "enhance": true,
      "postcss": true,
      "minified": true,
      "newFeature": false,
      "coverView": true,
      "nodeModules": false,
      "autoAudits": false,
      "showShadowRootInWxmlPanel": true,
      "scopeDataCheck": false,
      "uglifyFileName": false,
      "checkInvalidKey": true,
      "checkSiteMap": true,
      "uploadWithSourceMap": true,
      "compileHotReLoad": false,
      "lazyloadPlaceholderEnable": false,
      "useMultiFrameRuntime": true,
      "useApiHook": true,
      "useApiHostProcess": true,
      "babelSetting": {
        "ignore": [],
        "disablePlugins": [],
        "outputPath": ""
      }
    },
    "usingComponents": true,
    "permission": {
      "scope.userLocation": {
        "desc": "获取您的位置信息用于商家地址定位"
      },
      "scope.camera": {
        "desc": "需要使用您的摄像头扫描二维码"
      }
    },
    "requiredPrivateInfos": [
      "getLocation",
      "chooseLocation"
    ]
  },
  
  "vueVersion": "3"
}
```

### pages.json配置
```json
{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^merchant-(.*)": "@/components/merchant/merchant-$1.vue"
    }
  },
  
  "pages": [
    {
      "path": "pages/dashboard/index",
      "style": {
        "navigationBarTitleText": "商家中心",
        "enablePullDownRefresh": true,
        "onReachBottomDistance": 50
      }
    },
    {
      "path": "pages/product/index",
      "style": {
        "navigationBarTitleText": "商品管理"
      }
    },
    {
      "path": "pages/order/index",
      "style": {
        "navigationBarTitleText": "订单管理"
      }
    },
    {
      "path": "pages/finance/index",
      "style": {
        "navigationBarTitleText": "财务管理"
      }
    },
    {
      "path": "pages/profile/index",
      "style": {
        "navigationBarTitleText": "商家信息"
      }
    }
  ],
  
  "subPackages": [
    {
      "root": "pages-merchant/product",
      "pages": [
        {
          "path": "add/index",
          "style": {
            "navigationBarTitleText": "添加商品"
          }
        },
        {
          "path": "edit/index",
          "style": {
            "navigationBarTitleText": "编辑商品"
          }
        },
        {
          "path": "detail/index",
          "style": {
            "navigationBarTitleText": "商品详情"
          }
        },
        {
          "path": "category/index",
          "style": {
            "navigationBarTitleText": "商品分类"
          }
        }
      ]
    },
    {
      "root": "pages-merchant/order",
      "pages": [
        {
          "path": "list/index",
          "style": {
            "navigationBarTitleText": "订单列表",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "detail/index",
          "style": {
            "navigationBarTitleText": "订单详情"
          }
        },
        {
          "path": "ship/index",
          "style": {
            "navigationBarTitleText": "订单发货"
          }
        }
      ]
    },
    {
      "root": "pages-merchant/finance",
      "pages": [
        {
          "path": "revenue/index",
          "style": {
            "navigationBarTitleText": "营收统计"
          }
        },
        {
          "path": "withdraw/index",
          "style": {
            "navigationBarTitleText": "提现管理"
          }
        },
        {
          "path": "statement/index",
          "style": {
            "navigationBarTitleText": "财务报表"
          }
        }
      ]
    },
    {
      "root": "pages-merchant/marketing",
      "pages": [
        {
          "path": "coupon/index",
          "style": {
            "navigationBarTitleText": "优惠券管理"
          }
        },
        {
          "path": "activity/index",
          "style": {
            "navigationBarTitleText": "营销活动"
          }
        }
      ]
    },
    {
      "root": "pages-merchant/customer",
      "pages": [
        {
          "path": "list/index",
          "style": {
            "navigationBarTitleText": "客户管理"
          }
        },
        {
          "path": "service/index",
          "style": {
            "navigationBarTitleText": "客服中心"
          }
        }
      ]
    },
    {
      "root": "pages-merchant/auth",
      "pages": [
        {
          "path": "login/index",
          "style": {
            "navigationBarTitleText": "商家登录"
          }
        },
        {
          "path": "register/index",
          "style": {
            "navigationBarTitleText": "商家注册"
          }
        }
      ]
    }
  ],
  
  "preloadRule": {
    "pages/dashboard/index": {
      "network": "all",
      "packages": ["pages-merchant/product", "pages-merchant/order"]
    }
  },
  
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#667eea",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/dashboard/index",
        "iconPath": "static/tabbar/dashboard.png",
        "selectedIconPath": "static/tabbar/dashboard-active.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/product/index",
        "iconPath": "static/tabbar/product.png",
        "selectedIconPath": "static/tabbar/product-active.png",
        "text": "商品"
      },
      {
        "pagePath": "pages/order/index",
        "iconPath": "static/tabbar/order.png",
        "selectedIconPath": "static/tabbar/order-active.png",
        "text": "订单"
      },
      {
        "pagePath": "pages/finance/index",
        "iconPath": "static/tabbar/finance.png",
        "selectedIconPath": "static/tabbar/finance-active.png",
        "text": "财务"
      },
      {
        "pagePath": "pages/profile/index",
        "iconPath": "static/tabbar/profile.png",
        "selectedIconPath": "static/tabbar/profile-active.png",
        "text": "我的"
      }
    ]
  },
  
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "八闽助业集市商家端",
    "navigationBarBackgroundColor": "#ffffff",
    "backgroundColor": "#f7f8fa"
  }
}
```

## ⚙️ 构建脚本配置

### package.json脚本
```json
{
  "name": "heartful-mall-merchants-pro",
  "version": "1.0.0",
  "description": "八闽助业集市商家端小程序",
  "scripts": {
    "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build",
    "build:mp-weixin:dev": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch",
    "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build",
    "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build",
    "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch",
    "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve",
    "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch",
    "lint": "eslint --ext .js,.vue src",
    "lint:fix": "eslint --ext .js,.vue src --fix",
    "test": "jest",
    "analyze": "cross-env UNI_ANALYZE=true npm run build:mp-weixin",
    "deploy:test": "npm run build:mp-weixin && node scripts/deploy-test.js",
    "deploy:prod": "npm run build:mp-weixin && node scripts/deploy-prod.js"
  },
  "dependencies": {
    "@dcloudio/uni-app": "^3.0.0",
    "vue": "^3.2.0",
    "pinia": "^2.0.0",
    "luch-request": "^3.0.0",
    "echarts": "^5.4.0",
    "dayjs": "^1.11.0"
  },
  "devDependencies": {
    "@dcloudio/types": "^3.0.0",
    "@vue/cli-service": "^5.0.0",
    "cross-env": "^7.0.0",
    "eslint": "^8.0.0",
    "jest": "^29.0.0"
  }
}
```

### vue.config.js配置
```javascript
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = {
  configureWebpack: {
    resolve: {
      alias: {
        '@': resolve('src'),
        '@components': resolve('src/components'),
        '@merchant': resolve('src/components/merchant'),
        '@utils': resolve('src/utils'),
        '@api': resolve('src/api'),
        '@stores': resolve('src/stores'),
        '@static': resolve('src/static')
      }
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            chunks: 'all',
            priority: 10
          },
          echarts: {
            name: 'echarts',
            test: /[\\/]node_modules[\\/]echarts[\\/]/,
            chunks: 'all',
            priority: 20
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 5,
            reuseExistingChunk: true
          }
        }
      }
    }
  },
  
  chainWebpack: config => {
    // 图片压缩
    config.module
      .rule('images')
      .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
      .use('image-webpack-loader')
      .loader('image-webpack-loader')
      .options({
        mozjpeg: { progressive: true, quality: 75 },
        optipng: { enabled: false },
        pngquant: { quality: [0.6, 0.8], speed: 4 },
        gifsicle: { interlaced: false }
      })
  }
}
```

## 🌍 环境配置

### .env.development
```bash
# 开发环境
NODE_ENV=development
VUE_APP_ENV=development

# API接口地址
VUE_APP_API_BASE_URL=http://localhost:8080

# 商家端专用接口前缀
VUE_APP_MERCHANT_API_PREFIX=/back

# 文件上传地址
VUE_APP_UPLOAD_URL=http://localhost:8080/common/upload

# 微信小程序配置
VUE_APP_WECHAT_APPID=wxMERCHANT_XXXXXXXX

# 是否启用调试
VUE_APP_DEBUG=true

# 是否启用mock数据
VUE_APP_MOCK=false
```

### .env.production
```bash
# 生产环境
NODE_ENV=production
VUE_APP_ENV=production

# API接口地址
VUE_APP_API_BASE_URL=https://api.heartful-mall.com

# 商家端专用接口前缀
VUE_APP_MERCHANT_API_PREFIX=/back

# 文件上传地址
VUE_APP_UPLOAD_URL=https://api.heartful-mall.com/common/upload

# 微信小程序配置
VUE_APP_WECHAT_APPID=wxMERCHANT_XXXXXXXX

# 是否启用调试
VUE_APP_DEBUG=false

# 是否启用mock数据
VUE_APP_MOCK=false
```

## 🚀 部署流程

### 商家端小程序部署脚本
```bash
#!/bin/bash
# deploy-merchant-weixin.sh

set -e

echo "开始构建商家端小程序..."

# 检查环境
if [ "$1" != "test" ] && [ "$1" != "prod" ]; then
  echo "请指定部署环境: test 或 prod"
  echo "使用方法: ./deploy-merchant-weixin.sh [test|prod]"
  exit 1
fi

ENV=$1
echo "部署环境: $ENV"

# 安装依赖
echo "安装依赖..."
npm ci

# 运行测试
echo "运行测试..."
npm run test

# 代码质量检查
echo "代码质量检查..."
npm run lint

# 构建项目
echo "构建项目..."
if [ "$ENV" = "test" ]; then
  cp .env.test .env.production
else
  cp .env.production .env.production
fi

npm run build:mp-weixin

# 检查构建结果
BUILD_DIR="dist/build/mp-weixin"
if [ ! -d "$BUILD_DIR" ]; then
  echo "构建失败：未找到构建目录"
  exit 1
fi

echo "构建完成！"
echo "构建目录: $BUILD_DIR"
echo "请使用微信开发者工具打开该目录进行预览和上传"

# 生成部署报告
echo "生成部署报告..."
cat > deploy-report.md << EOF
# 商家端小程序部署报告

## 部署信息
- 部署时间: $(date '+%Y-%m-%d %H:%M:%S')
- 部署环境: $ENV
- 版本号: $(node -p "require('./package.json').version")
- 构建目录: $BUILD_DIR

## 构建统计
- 包大小: $(du -sh $BUILD_DIR | cut -f1)
- 文件数量: $(find $BUILD_DIR -type f | wc -l)

## 下一步操作
1. 使用微信开发者工具打开构建目录
2. 进行功能测试
3. 上传代码到微信平台
4. 提交审核
EOF

echo "部署报告已生成: deploy-report.md"
```

### 自动化部署脚本
```javascript
// scripts/deploy-prod.js
const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

class MerchantDeployment {
  constructor() {
    this.buildDir = 'dist/build/mp-weixin'
    this.deployConfig = {
      test: {
        appId: 'wxMERCHANT_TEST_XXXX',
        version: '1.0.0-test',
        description: '商家端测试版本'
      },
      prod: {
        appId: 'wxMERCHANT_PROD_XXXX',
        version: '1.0.0',
        description: '商家端正式版本'
      }
    }
  }
  
  // 检查构建环境
  checkEnvironment() {
    console.log('🔍 检查构建环境...')
    
    // 检查Node.js版本
    const nodeVersion = process.version
    console.log(`Node.js版本: ${nodeVersion}`)
    
    // 检查依赖
    if (!fs.existsSync('node_modules')) {
      console.log('📦 安装依赖...')
      execSync('npm ci', { stdio: 'inherit' })
    }
    
    console.log('✅ 环境检查完成')
  }
  
  // 运行测试
  runTests() {
    console.log('🧪 运行测试...')
    try {
      execSync('npm run test', { stdio: 'inherit' })
      console.log('✅ 测试通过')
    } catch (error) {
      console.error('❌ 测试失败')
      process.exit(1)
    }
  }
  
  // 代码质量检查
  lintCode() {
    console.log('🔍 代码质量检查...')
    try {
      execSync('npm run lint', { stdio: 'inherit' })
      console.log('✅ 代码质量检查通过')
    } catch (error) {
      console.error('❌ 代码质量检查失败')
      process.exit(1)
    }
  }
  
  // 构建项目
  buildProject(env = 'prod') {
    console.log(`🏗️ 构建${env}环境...`)
    
    try {
      // 设置环境变量
      const envFile = env === 'prod' ? '.env.production' : '.env.test'
      if (fs.existsSync(envFile)) {
        fs.copyFileSync(envFile, '.env.local')
      }
      
      // 执行构建
      execSync('npm run build:mp-weixin', { stdio: 'inherit' })
      
      // 检查构建结果
      if (!fs.existsSync(this.buildDir)) {
        throw new Error('构建目录不存在')
      }
      
      console.log('✅ 构建完成')
    } catch (error) {
      console.error('❌ 构建失败:', error.message)
      process.exit(1)
    }
  }
  
  // 生成部署报告
  generateReport(env) {
    console.log('📊 生成部署报告...')
    
    const config = this.deployConfig[env]
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
    
    const report = {
      deployTime: new Date().toISOString(),
      environment: env,
      version: packageJson.version,
      appId: config.appId,
      buildDir: this.buildDir,
      buildSize: this.getBuildSize(),
      fileCount: this.getFileCount()
    }
    
    fs.writeFileSync('deploy-report.json', JSON.stringify(report, null, 2))
    console.log('✅ 部署报告已生成: deploy-report.json')
    
    return report
  }
  
  // 获取构建大小
  getBuildSize() {
    try {
      const stats = execSync(`du -sh ${this.buildDir}`, { encoding: 'utf8' })
      return stats.split('\t')[0]
    } catch (error) {
      return 'Unknown'
    }
  }
  
  // 获取文件数量
  getFileCount() {
    try {
      const count = execSync(`find ${this.buildDir} -type f | wc -l`, { encoding: 'utf8' })
      return parseInt(count.trim())
    } catch (error) {
      return 0
    }
  }
  
  // 主部署流程
  async deploy(env = 'prod') {
    console.log(`🚀 开始部署商家端小程序 (${env})...`)
    
    try {
      this.checkEnvironment()
      this.runTests()
      this.lintCode()
      this.buildProject(env)
      const report = this.generateReport(env)
      
      console.log('\n🎉 部署完成！')
      console.log(`📁 构建目录: ${this.buildDir}`)
      console.log(`📊 包大小: ${report.buildSize}`)
      console.log(`📄 文件数量: ${report.fileCount}`)
      console.log('\n📋 下一步操作:')
      console.log('1. 使用微信开发者工具打开构建目录')
      console.log('2. 进行功能测试')
      console.log('3. 上传代码到微信平台')
      console.log('4. 提交审核')
      
    } catch (error) {
      console.error('❌ 部署失败:', error.message)
      process.exit(1)
    }
  }
}

// 执行部署
const env = process.argv[2] || 'prod'
const deployment = new MerchantDeployment()
deployment.deploy(env)
```

## 📊 性能优化

### 商家端特定优化
```javascript
// 图表组件懒加载
const ECharts = () => import(/* webpackChunkName: "echarts" */ '@/components/ECharts.vue')

// 大数据列表虚拟滚动
const VirtualList = () => import(/* webpackChunkName: "virtual-list" */ '@/components/VirtualList.vue')

// 商家端专用组件分包
const MerchantComponents = {
  ProductManager: () => import(/* webpackChunkName: "merchant-product" */ '@/components/merchant/ProductManager.vue'),
  OrderManager: () => import(/* webpackChunkName: "merchant-order" */ '@/components/merchant/OrderManager.vue'),
  FinanceManager: () => import(/* webpackChunkName: "merchant-finance" */ '@/components/merchant/FinanceManager.vue')
}
```

## 📋 部署检查清单

### 构建前检查
- [ ] 确认商家端API接口配置
- [ ] 验证商家认证流程
- [ ] 检查权限控制逻辑
- [ ] 测试数据统计功能
- [ ] 验证支付和财务功能

### 小程序发布检查
- [ ] 检查商家端小程序配置
- [ ] 验证商家专用功能权限
- [ ] 测试商品管理流程
- [ ] 验证订单处理功能
- [ ] 检查财务数据准确性

### 上线后验证
- [ ] 商家登录注册流程
- [ ] 商品CRUD操作
- [ ] 订单状态管理
- [ ] 数据统计准确性
- [ ] 消息通知功能

---

**文档说明**: 本文档为八闽助业集市商家端小程序构建部署规范，专注于商家业务场景的构建配置和部署流程。
