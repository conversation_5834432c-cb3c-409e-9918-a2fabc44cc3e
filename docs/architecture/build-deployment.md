# 八闽助业集市C端小程序构建部署规范

## 🏗️ UniApp构建配置

### 项目结构概述
```
┌─────────────────────────────────────────────────────────────────┐
│                    UniApp项目结构                               │
├─────────────────────────────────────────────────────────────────┤
│  manifest.json   → 应用配置文件                                │
│  pages.json      → 页面路由配置                                │
│  uni.scss        → 全局样式变量                                │
│  App.vue         → 应用入口组件                                │
│  main.js         → 应用入口文件                                │
│  /pages          → 页面文件目录                                │
│  /components     → 组件文件目录                                │
│  /static         → 静态资源目录                                │
│  /utils          → 工具函数目录                                │
└─────────────────────────────────────────────────────────────────┘
```

### manifest.json配置
```json
{
  "name": "八闽助业集市",
  "appid": "__UNI__HEARTFUL_MALL",
  "description": "八闽助业集市C端小程序",
  "versionName": "1.0.0",
  "versionCode": "100",
  "transformPx": false,
  
  "app-plus": {
    "usingComponents": true,
    "nvueStyleCompiler": "uni-app",
    "compilerVersion": 3,
    "splashscreen": {
      "alwaysShowBeforeRender": true,
      "waiting": true,
      "autoclose": true,
      "delay": 0
    },
    "modules": {},
    "distribute": {
      "android": {
        "permissions": [
          "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\" />",
          "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\" />",
          "<uses-permission android:name=\"android.permission.VIBRATE\" />",
          "<uses-permission android:name=\"android.permission.READ_LOGS\" />",
          "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\" />",
          "<uses-feature android:name=\"android.hardware.camera.autofocus\" />",
          "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\" />",
          "<uses-permission android:name=\"android.permission.CAMERA\" />",
          "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\" />",
          "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\" />",
          "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\" />",
          "<uses-permission android:name=\"android.permission.WAKE_LOCK\" />",
          "<uses-permission android:name=\"android.permission.FLASHLIGHT\" />",
          "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\" />"
        ]
      },
      "ios": {},
      "sdkConfigs": {
        "ad": {},
        "payment": {
          "weixin": {
            "appid": "wxXXXXXXXXXXXXXXXX"
          },
          "alipay": {
            "scheme": "alipayXXXXXXXXXXXXXXXX"
          }
        },
        "oauth": {
          "weixin": {
            "appid": "wxXXXXXXXXXXXXXXXX",
            "appsecret": "XXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
            "UniversalLinks": ""
          }
        },
        "share": {
          "weixin": {
            "appid": "wxXXXXXXXXXXXXXXXX",
            "UniversalLinks": ""
          }
        },
        "push": {
          "unipush": {}
        }
      }
    }
  },
  
  "quickapp": {},
  
  "mp-weixin": {
    "appid": "wxXXXXXXXXXXXXXXXX",
    "setting": {
      "urlCheck": false,
      "es6": true,
      "enhance": true,
      "postcss": true,
      "preloadBackgroundData": false,
      "minified": true,
      "newFeature": false,
      "coverView": true,
      "nodeModules": false,
      "autoAudits": false,
      "showShadowRootInWxmlPanel": true,
      "scopeDataCheck": false,
      "uglifyFileName": false,
      "checkInvalidKey": true,
      "checkSiteMap": true,
      "uploadWithSourceMap": true,
      "compileHotReLoad": false,
      "lazyloadPlaceholderEnable": false,
      "useMultiFrameRuntime": true,
      "useApiHook": true,
      "useApiHostProcess": true,
      "babelSetting": {
        "ignore": [],
        "disablePlugins": [],
        "outputPath": ""
      },
      "enableEngineNative": false,
      "useIsolateContext": false,
      "userConfirmedBundleSwitch": false,
      "packNpmManually": false,
      "packNpmRelationList": [],
      "minifyWXSS": true,
      "disableUseStrict": false,
      "minifyWXML": true,
      "showES6CompileOption": false,
      "useCompilerPlugins": false
    },
    "usingComponents": true,
    "permission": {
      "scope.userLocation": {
        "desc": "您的位置信息将用于小程序位置接口的效果展示"
      }
    },
    "requiredPrivateInfos": [
      "getLocation"
    ]
  },
  
  "mp-alipay": {
    "usingComponents": true,
    "appid": "XXXXXXXXXXXXXXXX",
    "component2": true
  },
  
  "mp-baidu": {
    "usingComponents": true,
    "appid": "XXXXXXXXXXXXXXXX"
  },
  
  "mp-toutiao": {
    "usingComponents": true,
    "appid": "XXXXXXXXXXXXXXXX"
  },
  
  "uniStatistics": {
    "enable": false
  },
  
  "vueVersion": "3"
}
```

### pages.json配置
```json
{
  "easycom": {
    "autoscan": true,
    "custom": {
      "^uv-(.*)": "@climblee/uv-ui/components/uv-$1/uv-$1.vue"
    }
  },
  
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页",
        "enablePullDownRefresh": true,
        "onReachBottomDistance": 50
      }
    },
    {
      "path": "pages/category/index",
      "style": {
        "navigationBarTitleText": "分类"
      }
    },
    {
      "path": "pages/cart/index",
      "style": {
        "navigationBarTitleText": "购物车"
      }
    },
    {
      "path": "pages/user/index",
      "style": {
        "navigationBarTitleText": "我的"
      }
    }
  ],
  
  "subPackages": [
    {
      "root": "pages-sub/product",
      "pages": [
        {
          "path": "detail/index",
          "style": {
            "navigationBarTitleText": "商品详情"
          }
        },
        {
          "path": "list/index",
          "style": {
            "navigationBarTitleText": "商品列表",
            "enablePullDownRefresh": true
          }
        }
      ]
    },
    {
      "root": "pages-sub/order",
      "pages": [
        {
          "path": "confirm/index",
          "style": {
            "navigationBarTitleText": "确认订单"
          }
        },
        {
          "path": "list/index",
          "style": {
            "navigationBarTitleText": "我的订单",
            "enablePullDownRefresh": true
          }
        },
        {
          "path": "detail/index",
          "style": {
            "navigationBarTitleText": "订单详情"
          }
        }
      ]
    },
    {
      "root": "pages-sub/user",
      "pages": [
        {
          "path": "login/index",
          "style": {
            "navigationBarTitleText": "登录"
          }
        },
        {
          "path": "profile/index",
          "style": {
            "navigationBarTitleText": "个人信息"
          }
        },
        {
          "path": "address/index",
          "style": {
            "navigationBarTitleText": "收货地址",
            "enablePullDownRefresh": true
          }
        }
      ]
    }
  ],
  
  "preloadRule": {
    "pages/index/index": {
      "network": "all",
      "packages": ["pages-sub/product"]
    }
  },
  
  "tabBar": {
    "color": "#7A7E83",
    "selectedColor": "#667eea",
    "borderStyle": "black",
    "backgroundColor": "#ffffff",
    "list": [
      {
        "pagePath": "pages/index/index",
        "iconPath": "static/tabbar/home.png",
        "selectedIconPath": "static/tabbar/home-active.png",
        "text": "首页"
      },
      {
        "pagePath": "pages/category/index",
        "iconPath": "static/tabbar/category.png",
        "selectedIconPath": "static/tabbar/category-active.png",
        "text": "分类"
      },
      {
        "pagePath": "pages/cart/index",
        "iconPath": "static/tabbar/cart.png",
        "selectedIconPath": "static/tabbar/cart-active.png",
        "text": "购物车"
      },
      {
        "pagePath": "pages/user/index",
        "iconPath": "static/tabbar/user.png",
        "selectedIconPath": "static/tabbar/user-active.png",
        "text": "我的"
      }
    ]
  },
  
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "八闽助业集市",
    "navigationBarBackgroundColor": "#ffffff",
    "backgroundColor": "#f7f8fa",
    "app-plus": {
      "background": "#efeff4"
    }
  },
  
  "condition": {
    "current": 0,
    "list": [
      {
        "name": "商品详情",
        "path": "pages-sub/product/detail/index",
        "query": "id=1"
      }
    ]
  }
}
```

## ⚙️ 构建脚本配置

### package.json脚本
```json
{
  "name": "heartful-mall-app",
  "version": "1.0.0",
  "description": "八闽助业集市C端小程序",
  "main": "main.js",
  "scripts": {
    "build:mp-weixin": "cross-env NODE_ENV=production UNI_PLATFORM=mp-weixin vue-cli-service uni-build",
    "build:mp-weixin:dev": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch",
    "build:mp-alipay": "cross-env NODE_ENV=production UNI_PLATFORM=mp-alipay vue-cli-service uni-build",
    "build:mp-baidu": "cross-env NODE_ENV=production UNI_PLATFORM=mp-baidu vue-cli-service uni-build",
    "build:mp-toutiao": "cross-env NODE_ENV=production UNI_PLATFORM=mp-toutiao vue-cli-service uni-build",
    "build:h5": "cross-env NODE_ENV=production UNI_PLATFORM=h5 vue-cli-service uni-build",
    "build:h5:dev": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-build --watch",
    "build:app-plus": "cross-env NODE_ENV=production UNI_PLATFORM=app-plus vue-cli-service uni-build",
    "dev:mp-weixin": "cross-env NODE_ENV=development UNI_PLATFORM=mp-weixin vue-cli-service uni-build --watch",
    "dev:mp-alipay": "cross-env NODE_ENV=development UNI_PLATFORM=mp-alipay vue-cli-service uni-build --watch",
    "dev:mp-baidu": "cross-env NODE_ENV=development UNI_PLATFORM=mp-baidu vue-cli-service uni-build --watch",
    "dev:mp-toutiao": "cross-env NODE_ENV=development UNI_PLATFORM=mp-toutiao vue-cli-service uni-build --watch",
    "dev:h5": "cross-env NODE_ENV=development UNI_PLATFORM=h5 vue-cli-service uni-serve",
    "dev:app-plus": "cross-env NODE_ENV=development UNI_PLATFORM=app-plus vue-cli-service uni-build --watch",
    "lint": "eslint --ext .js,.vue src",
    "lint:fix": "eslint --ext .js,.vue src --fix",
    "test": "jest",
    "analyze": "cross-env UNI_ANALYZE=true npm run build:mp-weixin"
  },
  "dependencies": {
    "@dcloudio/uni-app": "^3.0.0",
    "@dcloudio/uni-components": "^3.0.0",
    "@dcloudio/uni-h5": "^3.0.0",
    "@dcloudio/uni-mp-alipay": "^3.0.0",
    "@dcloudio/uni-mp-baidu": "^3.0.0",
    "@dcloudio/uni-mp-qq": "^3.0.0",
    "@dcloudio/uni-mp-toutiao": "^3.0.0",
    "@dcloudio/uni-mp-weixin": "^3.0.0",
    "@dcloudio/uni-quickapp-webview": "^3.0.0",
    "vue": "^3.2.0",
    "pinia": "^2.0.0",
    "luch-request": "^3.0.0",
    "@climblee/uv-ui": "^1.0.0"
  },
  "devDependencies": {
    "@dcloudio/types": "^3.0.0",
    "@dcloudio/uni-automator": "^3.0.0",
    "@dcloudio/uni-cli-shared": "^3.0.0",
    "@dcloudio/webpack-uni-mp-loader": "^3.0.0",
    "@vue/cli-plugin-babel": "^5.0.0",
    "@vue/cli-service": "^5.0.0",
    "babel-plugin-import": "^1.13.0",
    "cross-env": "^7.0.0",
    "jest": "^25.4.0",
    "postcss-comment": "^2.0.0"
  },
  "browserslist": [
    "Android >= 4.4",
    "ios >= 9"
  ],
  "uni-app": {
    "scripts": {}
  }
}
```

### vue.config.js配置
```javascript
const path = require('path')

function resolve(dir) {
  return path.join(__dirname, dir)
}

module.exports = {
  configureWebpack: {
    resolve: {
      alias: {
        '@': resolve('src'),
        '@components': resolve('src/components'),
        '@utils': resolve('src/utils'),
        '@api': resolve('src/api'),
        '@stores': resolve('src/stores'),
        '@static': resolve('src/static')
      }
    },
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            chunks: 'all',
            priority: 10
          },
          common: {
            name: 'common',
            minChunks: 2,
            chunks: 'all',
            priority: 5,
            reuseExistingChunk: true
          }
        }
      }
    }
  },
  
  chainWebpack: config => {
    // 图片压缩
    config.module
      .rule('images')
      .test(/\.(png|jpe?g|gif|svg)(\?.*)?$/)
      .use('image-webpack-loader')
      .loader('image-webpack-loader')
      .options({
        mozjpeg: { progressive: true, quality: 80 },
        optipng: { enabled: false },
        pngquant: { quality: [0.65, 0.8], speed: 4 },
        gifsicle: { interlaced: false }
      })
  },
  
  // 开发服务器配置
  devServer: {
    port: 8080,
    open: false,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/api': {
        target: process.env.VUE_APP_API_BASE_URL,
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  }
}
```

## 🌍 环境配置

### .env.development
```bash
# 开发环境
NODE_ENV=development
VUE_APP_ENV=development

# API接口地址
VUE_APP_API_BASE_URL=http://localhost:8080

# 文件上传地址
VUE_APP_UPLOAD_URL=http://localhost:8080/common/upload

# 文件预览地址
VUE_APP_FILE_VIEW_URL=http://localhost:8080/common/view

# 微信小程序配置
VUE_APP_WECHAT_APPID=wxXXXXXXXXXXXXXXXX

# 是否启用调试
VUE_APP_DEBUG=true

# 是否启用mock数据
VUE_APP_MOCK=false
```

### .env.production
```bash
# 生产环境
NODE_ENV=production
VUE_APP_ENV=production

# API接口地址
VUE_APP_API_BASE_URL=https://api.heartful-mall.com

# 文件上传地址
VUE_APP_UPLOAD_URL=https://api.heartful-mall.com/common/upload

# 文件预览地址
VUE_APP_FILE_VIEW_URL=https://api.heartful-mall.com/common/view

# 微信小程序配置
VUE_APP_WECHAT_APPID=wxXXXXXXXXXXXXXXXX

# 是否启用调试
VUE_APP_DEBUG=false

# 是否启用mock数据
VUE_APP_MOCK=false
```

## 🚀 部署流程

### 微信小程序部署
```bash
#!/bin/bash
# deploy-weixin.sh

set -e

echo "开始构建微信小程序..."

# 安装依赖
npm ci

# 构建生产版本
npm run build:mp-weixin

# 检查构建结果
if [ ! -d "dist/build/mp-weixin" ]; then
  echo "构建失败：未找到构建目录"
  exit 1
fi

echo "构建完成，请使用微信开发者工具打开 dist/build/mp-weixin 目录进行预览和上传"

# 可选：自动打开微信开发者工具
# /Applications/wechatwebdevtools.app/Contents/MacOS/cli -o dist/build/mp-weixin
```

### H5部署脚本
```bash
#!/bin/bash
# deploy-h5.sh

set -e

PROJECT_NAME="heartful-mall-app-h5"
BUILD_DIR="dist/build/h5"
DEPLOY_DIR="/var/www/heartful-mall-h5"
BACKUP_DIR="/var/backups/heartful-mall-h5"

echo "开始部署H5版本..."

# 构建项目
npm ci
npm run build:h5

# 检查构建结果
if [ ! -d "$BUILD_DIR" ]; then
  echo "构建失败：未找到构建目录"
  exit 1
fi

# 备份当前版本
if [ -d "$DEPLOY_DIR" ]; then
  mkdir -p $BACKUP_DIR
  cp -r $DEPLOY_DIR $BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S)
fi

# 部署新版本
mkdir -p $DEPLOY_DIR
cp -r $BUILD_DIR/* $DEPLOY_DIR/

# 设置权限
chown -R www-data:www-data $DEPLOY_DIR
chmod -R 755 $DEPLOY_DIR

# 重启nginx
nginx -t && systemctl reload nginx

echo "H5版本部署完成"
```

## 📊 性能优化

### 代码分割配置
```javascript
// 路由懒加载
const routes = [
  {
    path: '/product/detail',
    component: () => import(/* webpackChunkName: "product" */ '@/pages-sub/product/detail/index.vue')
  },
  {
    path: '/order/list',
    component: () => import(/* webpackChunkName: "order" */ '@/pages-sub/order/list/index.vue')
  }
]

// 组件懒加载
export default {
  components: {
    ProductCard: () => import('@/components/ProductCard.vue')
  }
}
```

### 资源优化
```javascript
// 图片懒加载配置
const imageOptions = {
  loading: '/static/images/loading.png',
  error: '/static/images/error.png',
  attempt: 3
}

// 预加载关键资源
const preloadResources = [
  '/static/images/logo.png',
  '/static/fonts/iconfont.woff2'
]
```

## 📋 部署检查清单

### 构建前检查
- [ ] 确认环境变量配置
- [ ] 检查依赖版本兼容性
- [ ] 运行代码质量检查
- [ ] 执行单元测试
- [ ] 验证API接口连通性

### 小程序发布检查
- [ ] 检查小程序配置信息
- [ ] 验证权限申请合理性
- [ ] 测试核心功能流程
- [ ] 检查页面性能指标
- [ ] 验证支付功能正常

### H5部署检查
- [ ] 检查域名配置
- [ ] 验证SSL证书
- [ ] 测试移动端适配
- [ ] 检查SEO优化
- [ ] 验证分享功能

---

**文档说明**: 本文档为八闽助业集市C端小程序构建部署规范，涵盖UniApp多端构建和部署的完整流程。
