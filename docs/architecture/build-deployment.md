# 八闽助业集市管理后台构建部署规范

## 🏗️ 构建配置概述

### 构建工具链
```
┌─────────────────────────────────────────────────────────────────┐
│                        构建工具链                                │
├─────────────────────────────────────────────────────────────────┤
│  Vue CLI 4.x    → 项目脚手架和构建工具                          │
│  Webpack 4.x    → 模块打包器                                   │
│  Babel 7.x      → JavaScript编译器                             │
│  ESLint         → 代码质量检查                                  │
│  Prettier       → 代码格式化                                   │
│  Less           → CSS预处理器                                  │
└─────────────────────────────────────────────────────────────────┘
```

### 环境配置
- **开发环境** (development): 本地开发调试
- **测试环境** (test): 功能测试验证
- **预生产环境** (staging): 生产前验证
- **生产环境** (production): 正式发布环境

## ⚙️ 项目配置文件

### package.json脚本配置
```json
{
  "name": "heartful-mall-web",
  "version": "1.0.0",
  "scripts": {
    "serve": "vue-cli-service serve",
    "build": "vue-cli-service build",
    "build:test": "vue-cli-service build --mode test",
    "build:staging": "vue-cli-service build --mode staging",
    "build:prod": "vue-cli-service build --mode production",
    "lint": "vue-cli-service lint",
    "lint:fix": "vue-cli-service lint --fix",
    "test:unit": "vue-cli-service test:unit",
    "analyze": "vue-cli-service build --report",
    "preview": "node build/index.js --preview",
    "deploy": "node build/deploy.js"
  },
  "dependencies": {
    "vue": "^2.6.10",
    "vue-router": "^3.1.3",
    "vuex": "^3.1.2",
    "ant-design-vue": "^1.7.2",
    "axios": "^0.21.1",
    "moment": "^2.24.0",
    "lodash": "^4.17.15"
  },
  "devDependencies": {
    "@vue/cli-plugin-babel": "^4.4.0",
    "@vue/cli-plugin-eslint": "^4.4.0",
    "@vue/cli-plugin-router": "^4.4.0",
    "@vue/cli-plugin-vuex": "^4.4.0",
    "@vue/cli-service": "^4.4.0",
    "babel-eslint": "^10.1.0",
    "eslint": "^6.7.2",
    "eslint-plugin-vue": "^6.2.2",
    "less": "^3.0.4",
    "less-loader": "^5.0.0",
    "webpack-bundle-analyzer": "^3.8.0"
  }
}
```

### vue.config.js配置
```javascript
const path = require('path')
const webpack = require('webpack')
const CompressionWebpackPlugin = require('compression-webpack-plugin')
const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer')

function resolve(dir) {
  return path.join(__dirname, dir)
}

// 是否为生产环境
const isProduction = process.env.NODE_ENV !== 'development'

// 本地环境是否需要使用cdn
const devNeedCdn = false

// cdn链接
const cdn = {
  // cdn：模块名称和模块作用域命名（对应window里面挂载的变量名称）
  externals: {
    vue: 'Vue',
    vuex: 'Vuex',
    'vue-router': 'VueRouter',
    'ant-design-vue': 'antd',
    axios: 'axios',
    moment: 'moment'
  },
  // cdn的css链接
  css: [
    'https://cdn.jsdelivr.net/npm/ant-design-vue@1.7.2/dist/antd.min.css'
  ],
  // cdn的js链接
  js: [
    'https://cdn.jsdelivr.net/npm/vue@2.6.10/dist/vue.min.js',
    'https://cdn.jsdelivr.net/npm/vuex@3.1.2/dist/vuex.min.js',
    'https://cdn.jsdelivr.net/npm/vue-router@3.1.3/dist/vue-router.min.js',
    'https://cdn.jsdelivr.net/npm/ant-design-vue@1.7.2/dist/antd.min.js',
    'https://cdn.jsdelivr.net/npm/axios@0.21.1/dist/axios.min.js',
    'https://cdn.jsdelivr.net/npm/moment@2.24.0/moment.min.js'
  ]
}

module.exports = {
  publicPath: isProduction ? './' : '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: false,
  productionSourceMap: false,
  
  devServer: {
    port: 3000,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    proxy: {
      '/api': {
        target: process.env.VUE_APP_API_BASE_URL,
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      }
    }
  },
  
  css: {
    loaderOptions: {
      less: {
        modifyVars: {
          'primary-color': '#1890FF',
          'link-color': '#1890FF',
          'border-radius-base': '6px'
        },
        javascriptEnabled: true
      }
    }
  },
  
  configureWebpack: config => {
    // 用cdn方式引入，则构建时要忽略相关资源
    if (isProduction || devNeedCdn) config.externals = cdn.externals
    
    // 生产环境相关配置
    if (isProduction) {
      // 代码压缩
      config.plugins.push(
        new CompressionWebpackPlugin({
          algorithm: 'gzip',
          test: /\.js$|\.html$|\.json$|\.css/,
          threshold: 10240,
          minRatio: 0.8
        })
      )
      
      // 打包分析
      if (process.env.ANALYZE) {
        config.plugins.push(new BundleAnalyzerPlugin())
      }
    }
  },
  
  chainWebpack: config => {
    config.resolve.alias
      .set('@', resolve('src'))
      .set('assets', resolve('src/assets'))
      .set('components', resolve('src/components'))
      .set('views', resolve('src/views'))
    
    // 对vue-cli内部的webpack配置进行更细粒度的修改
    config.plugin('html').tap(args => {
      // 生产环境或本地需要cdn时，才注入cdn
      if (isProduction || devNeedCdn) args[0].cdn = cdn
      return args
    })
    
    // 生产环境，开启js\css压缩
    if (isProduction) {
      config.plugin('compressionPlugin').use(new CompressionWebpackPlugin({
        test: /\.(js|css|less)$/, // 匹配文件名
        threshold: 10240, // 对超过10k的数据压缩
        deleteOriginalAssets: false // 不删除源文件
      }))
    }
  }
}
```

## 🌍 环境变量配置

### .env.development
```bash
# 开发环境配置
NODE_ENV = 'development'
VUE_APP_ENV = 'development'

# 接口地址
VUE_APP_API_BASE_URL = 'http://localhost:8080'

# 文件上传地址
VUE_APP_UPLOAD_URL = 'http://localhost:8080/sys/common/upload'

# 文件预览地址
VUE_APP_FILE_VIEW_URL = 'http://localhost:8080/sys/common/view'

# WebSocket地址
VUE_APP_WEBSOCKET_URL = 'ws://localhost:8080/websocket'

# 是否启用mock
VUE_APP_MOCK = false

# 是否启用vconsole调试
VUE_APP_VCONSOLE = false
```

### .env.test
```bash
# 测试环境配置
NODE_ENV = 'production'
VUE_APP_ENV = 'test'

# 接口地址
VUE_APP_API_BASE_URL = 'https://test-api.heartful-mall.com'

# 文件上传地址
VUE_APP_UPLOAD_URL = 'https://test-api.heartful-mall.com/sys/common/upload'

# 文件预览地址
VUE_APP_FILE_VIEW_URL = 'https://test-api.heartful-mall.com/sys/common/view'

# WebSocket地址
VUE_APP_WEBSOCKET_URL = 'wss://test-api.heartful-mall.com/websocket'

# 是否启用mock
VUE_APP_MOCK = false

# 是否启用vconsole调试
VUE_APP_VCONSOLE = true
```

### .env.production
```bash
# 生产环境配置
NODE_ENV = 'production'
VUE_APP_ENV = 'production'

# 接口地址
VUE_APP_API_BASE_URL = 'https://api.heartful-mall.com'

# 文件上传地址
VUE_APP_UPLOAD_URL = 'https://api.heartful-mall.com/sys/common/upload'

# 文件预览地址
VUE_APP_FILE_VIEW_URL = 'https://api.heartful-mall.com/sys/common/view'

# WebSocket地址
VUE_APP_WEBSOCKET_URL = 'wss://api.heartful-mall.com/websocket'

# 是否启用mock
VUE_APP_MOCK = false

# 是否启用vconsole调试
VUE_APP_VCONSOLE = false
```

## 🔧 构建优化配置

### Webpack优化配置
```javascript
// vue.config.js 优化配置
module.exports = {
  configureWebpack: config => {
    if (isProduction) {
      // 代码分割
      config.optimization = {
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            libs: {
              name: 'chunk-libs',
              test: /[\\/]node_modules[\\/]/,
              priority: 10,
              chunks: 'initial'
            },
            antdv: {
              name: 'chunk-antdv',
              priority: 20,
              test: /[\\/]node_modules[\\/]_?ant-design-vue(.*)/
            },
            commons: {
              name: 'chunk-commons',
              test: resolve('src/components'),
              minChunks: 3,
              priority: 5,
              reuseExistingChunk: true
            }
          }
        }
      }
    }
  },
  
  chainWebpack: config => {
    // 预加载
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial'
      }
    ])
    
    // 预获取
    config.plugin('prefetch').tap(options => {
      options[0].fileBlacklist = options[0].fileBlacklist || []
      options[0].fileBlacklist.push(/chunk-libs\..*\.js$/, /chunk-antdv\..*\.js$/)
      return options
    })
    
    // 图片压缩
    config.module
      .rule('images')
      .use('image-webpack-loader')
      .loader('image-webpack-loader')
      .options({
        mozjpeg: { progressive: true, quality: 65 },
        optipng: { enabled: false },
        pngquant: { quality: [0.65, 0.90], speed: 4 },
        gifsicle: { interlaced: false }
      })
  }
}
```

### Babel配置
```javascript
// babel.config.js
module.exports = {
  presets: [
    '@vue/cli-plugin-babel/preset'
  ],
  plugins: [
    // 按需引入ant-design-vue
    [
      'import',
      {
        libraryName: 'ant-design-vue',
        libraryDirectory: 'es',
        style: true
      }
    ],
    // 移除console
    ...(process.env.NODE_ENV === 'production' ? [['transform-remove-console']] : [])
  ]
}
```

## 🚀 部署配置

### Nginx配置
```nginx
server {
    listen 80;
    server_name admin.heartful-mall.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name admin.heartful-mall.com;
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/heartful-mall.crt;
    ssl_certificate_key /etc/ssl/private/heartful-mall.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 根目录
    root /var/www/heartful-mall-web/dist;
    index index.html;
    
    # 日志配置
    access_log /var/log/nginx/heartful-mall-web-access.log;
    error_log /var/log/nginx/heartful-mall-web-error.log;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
        access_log off;
    }
    
    # HTML文件不缓存
    location ~* \.html$ {
        expires -1;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }
    
    # API代理
    location /api/ {
        proxy_pass https://api.heartful-mall.com/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 健康检查
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
```

### Docker部署配置
```dockerfile
# Dockerfile
FROM node:14-alpine as build-stage

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build:prod

# 生产阶段
FROM nginx:stable-alpine as production-stage

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 复制构建产物
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
```

### Docker Compose配置
```yaml
version: '3.8'

services:
  heartful-mall-web:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: heartful-mall-web
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/ssl/certs
      - ./logs:/var/log/nginx
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
```

## 📋 部署脚本

### 自动化部署脚本
```bash
#!/bin/bash
# deploy.sh

set -e

# 配置变量
PROJECT_NAME="heartful-mall-web"
BUILD_DIR="dist"
DEPLOY_DIR="/var/www/heartful-mall-web"
BACKUP_DIR="/var/backups/heartful-mall-web"
LOG_FILE="/var/log/deploy-${PROJECT_NAME}.log"

# 记录日志
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a $LOG_FILE
}

# 检查环境
check_environment() {
    log "检查部署环境..."
    
    if ! command -v node &> /dev/null; then
        log "错误: Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log "错误: npm 未安装"
        exit 1
    fi
    
    log "环境检查通过"
}

# 安装依赖
install_dependencies() {
    log "安装项目依赖..."
    npm ci --only=production
    log "依赖安装完成"
}

# 构建项目
build_project() {
    log "开始构建项目..."
    npm run build:prod
    
    if [ ! -d "$BUILD_DIR" ]; then
        log "错误: 构建失败，未找到构建目录"
        exit 1
    fi
    
    log "项目构建完成"
}

# 备份当前版本
backup_current() {
    log "备份当前版本..."
    
    if [ -d "$DEPLOY_DIR" ]; then
        mkdir -p $BACKUP_DIR
        cp -r $DEPLOY_DIR $BACKUP_DIR/backup-$(date +%Y%m%d-%H%M%S)
        log "备份完成"
    else
        log "无需备份，首次部署"
    fi
}

# 部署新版本
deploy_new() {
    log "部署新版本..."
    
    # 创建部署目录
    mkdir -p $DEPLOY_DIR
    
    # 复制构建文件
    cp -r $BUILD_DIR/* $DEPLOY_DIR/
    
    # 设置权限
    chown -R www-data:www-data $DEPLOY_DIR
    chmod -R 755 $DEPLOY_DIR
    
    log "部署完成"
}

# 重启服务
restart_service() {
    log "重启Nginx服务..."
    
    # 测试nginx配置
    nginx -t
    
    if [ $? -eq 0 ]; then
        systemctl reload nginx
        log "Nginx重启成功"
    else
        log "错误: Nginx配置测试失败"
        exit 1
    fi
}

# 健康检查
health_check() {
    log "执行健康检查..."
    
    sleep 5
    
    for i in {1..5}; do
        if curl -f http://localhost/health > /dev/null 2>&1; then
            log "健康检查通过"
            return 0
        fi
        log "健康检查失败，重试 $i/5"
        sleep 10
    done
    
    log "错误: 健康检查失败"
    return 1
}

# 回滚
rollback() {
    log "开始回滚..."
    
    LATEST_BACKUP=$(ls -t $BACKUP_DIR | head -n1)
    if [ -n "$LATEST_BACKUP" ]; then
        rm -rf $DEPLOY_DIR
        cp -r $BACKUP_DIR/$LATEST_BACKUP $DEPLOY_DIR
        systemctl reload nginx
        log "回滚完成"
    else
        log "错误: 没有找到备份文件"
        exit 1
    fi
}

# 主流程
main() {
    log "开始部署 $PROJECT_NAME"
    
    check_environment
    install_dependencies
    build_project
    backup_current
    deploy_new
    restart_service
    
    if health_check; then
        log "部署成功完成"
    else
        log "部署失败，开始回滚"
        rollback
        exit 1
    fi
}

# 执行部署
main "$@"
```

## 📊 性能监控

### 构建分析
```bash
# 分析构建包大小
npm run analyze

# 查看依赖关系
npm ls --depth=0

# 检查过期依赖
npm outdated
```

### 性能指标监控
```javascript
// src/utils/performance.js
export function reportPerformance() {
  if ('performance' in window) {
    const timing = performance.timing
    const navigation = performance.navigation
    
    const metrics = {
      // DNS查询时间
      dns: timing.domainLookupEnd - timing.domainLookupStart,
      // TCP连接时间
      tcp: timing.connectEnd - timing.connectStart,
      // 请求时间
      request: timing.responseEnd - timing.requestStart,
      // 解析DOM树时间
      domReady: timing.domContentLoadedEventEnd - timing.navigationStart,
      // 页面完全加载时间
      loadComplete: timing.loadEventEnd - timing.navigationStart,
      // 重定向次数
      redirectCount: navigation.redirectCount,
      // 导航类型
      navigationType: navigation.type
    }
    
    // 发送性能数据到监控系统
    console.log('Performance Metrics:', metrics)
    
    return metrics
  }
}
```

## 📋 构建部署检查清单

### 构建前检查
- [ ] 确认环境变量配置正确
- [ ] 检查依赖版本兼容性
- [ ] 运行代码质量检查
- [ ] 执行单元测试
- [ ] 检查构建配置

### 部署前检查
- [ ] 备份当前版本
- [ ] 检查服务器资源
- [ ] 验证SSL证书有效性
- [ ] 测试数据库连接
- [ ] 确认API接口可用

### 部署后验证
- [ ] 检查应用启动状态
- [ ] 验证页面访问正常
- [ ] 测试核心功能
- [ ] 检查日志输出
- [ ] 监控性能指标

---

**文档说明**: 本文档为八闽助业集市管理后台构建部署规范，涵盖了从开发构建到生产部署的完整流程。
