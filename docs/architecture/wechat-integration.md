# H5端微信集成架构文档

## 概述

八闽助业集市H5端专门用于微信公众号环境，集成微信JS SDK和企业微信SDK，提供完整的微信生态功能支持。

## 微信JS SDK集成

### SDK配置

#### 基础配置
```javascript
// utils/wechat.js
import wx from 'weixin-js-sdk'

class WechatSDK {
  constructor() {
    this.isReady = false
    this.config = {
      debug: false,
      appId: '',
      timestamp: '',
      nonceStr: '',
      signature: '',
      jsApiList: [
        'updateAppMessageShareData',
        'updateTimelineShareData', 
        'onMenuShareTimeline',
        'onMenuShareAppMessage',
        'chooseWXPay',
        'scanQRCode',
        'chooseImage',
        'uploadImage',
        'getLocation'
      ]
    }
  }

  // 初始化微信SDK
  async init() {
    try {
      const configData = await this.getWechatConfig()
      this.config = { ...this.config, ...configData }
      
      wx.config(this.config)
      
      return new Promise((resolve, reject) => {
        wx.ready(() => {
          this.isReady = true
          console.log('微信SDK初始化成功')
          resolve(true)
        })
        
        wx.error((res) => {
          console.error('微信SDK初始化失败:', res)
          reject(res)
        })
      })
    } catch (error) {
      console.error('获取微信配置失败:', error)
      throw error
    }
  }

  // 获取微信配置
  async getWechatConfig() {
    const url = encodeURIComponent(window.location.href.split('#')[0])
    const response = await uni.request({
      url: '/front/wechat/config',
      method: 'POST',
      data: { url }
    })
    
    if (response.data.success) {
      return response.data.result
    } else {
      throw new Error(response.data.message)
    }
  }
}

export default new WechatSDK()
```

#### 环境检测
```javascript
// utils/environment.js
export class EnvironmentDetector {
  // 检测是否在微信环境
  static isWechat() {
    const ua = navigator.userAgent.toLowerCase()
    return ua.includes('micromessenger')
  }

  // 检测是否在企业微信环境
  static isWorkWechat() {
    const ua = navigator.userAgent.toLowerCase()
    return ua.includes('wxwork')
  }

  // 检测是否在微信小程序环境
  static isMiniProgram() {
    return new Promise((resolve) => {
      if (!this.isWechat()) {
        resolve(false)
        return
      }
      
      wx.miniProgram.getEnv((res) => {
        resolve(res.miniprogram)
      })
    })
  }

  // 获取当前环境类型
  static getEnvironmentType() {
    if (this.isWorkWechat()) return 'work-wechat'
    if (this.isWechat()) return 'wechat'
    return 'browser'
  }
}
```

### 微信登录集成

#### 授权登录流程
```javascript
// utils/wechat-auth.js
export class WechatAuth {
  // 微信授权登录
  static async authorize() {
    const envType = EnvironmentDetector.getEnvironmentType()
    
    switch (envType) {
      case 'wechat':
        return await this.wechatAuthorize()
      case 'work-wechat':
        return await this.workWechatAuthorize()
      default:
        throw new Error('当前环境不支持微信登录')
    }
  }

  // 微信公众号授权
  static async wechatAuthorize() {
    const appId = 'your-wechat-app-id'
    const redirectUri = encodeURIComponent(window.location.href)
    const scope = 'snsapi_userinfo'
    const state = 'STATE'
    
    const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`
    
    window.location.href = authUrl
  }

  // 企业微信授权
  static async workWechatAuthorize() {
    const corpId = 'your-corp-id'
    const redirectUri = encodeURIComponent(window.location.href)
    const scope = 'snsapi_base'
    const state = 'STATE'
    
    const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${corpId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`
    
    window.location.href = authUrl
  }

  // 处理授权回调
  static async handleAuthCallback() {
    const urlParams = new URLSearchParams(window.location.search)
    const code = urlParams.get('code')
    const state = urlParams.get('state')
    
    if (!code) {
      throw new Error('授权失败，未获取到授权码')
    }
    
    try {
      const response = await uni.request({
        url: '/front/wechat/login',
        method: 'POST',
        data: { code, state }
      })
      
      if (response.data.success) {
        const { userToken, userInfo } = response.data.result
        
        // 保存用户信息
        uni.setStorageSync('userToken', userToken)
        uni.setStorageSync('userInfo', userInfo)
        
        return { userToken, userInfo }
      } else {
        throw new Error(response.data.message)
      }
    } catch (error) {
      console.error('微信登录失败:', error)
      throw error
    }
  }
}
```

### 微信分享功能

#### 分享配置
```javascript
// utils/wechat-share.js
export class WechatShare {
  // 配置分享内容
  static async configShare(shareData) {
    if (!wechatSDK.isReady) {
      await wechatSDK.init()
    }

    const defaultShareData = {
      title: '八闽助业集市',
      desc: '福建省内优质商品，助力乡村振兴',
      link: window.location.href,
      imgUrl: 'https://your-domain.com/logo.png'
    }

    const finalShareData = { ...defaultShareData, ...shareData }

    // 分享到朋友圈
    wx.updateTimelineShareData({
      title: finalShareData.title,
      link: finalShareData.link,
      imgUrl: finalShareData.imgUrl,
      success: () => {
        console.log('朋友圈分享配置成功')
      },
      fail: (error) => {
        console.error('朋友圈分享配置失败:', error)
      }
    })

    // 分享给朋友
    wx.updateAppMessageShareData({
      title: finalShareData.title,
      desc: finalShareData.desc,
      link: finalShareData.link,
      imgUrl: finalShareData.imgUrl,
      success: () => {
        console.log('好友分享配置成功')
      },
      fail: (error) => {
        console.error('好友分享配置失败:', error)
      }
    })
  }

  // 商品分享
  static async shareGoods(goodsInfo) {
    const shareData = {
      title: goodsInfo.name,
      desc: `${goodsInfo.description} - 八闽助业集市`,
      link: `${window.location.origin}/goods/detail?id=${goodsInfo.id}`,
      imgUrl: goodsInfo.image
    }

    await this.configShare(shareData)
  }

  // 店铺分享
  static async shareStore(storeInfo) {
    const shareData = {
      title: `${storeInfo.name} - 八闽助业集市`,
      desc: storeInfo.description,
      link: `${window.location.origin}/store/detail?id=${storeInfo.id}`,
      imgUrl: storeInfo.logo
    }

    await this.configShare(shareData)
  }

  // 邀请分享
  static async shareInvite(userInfo) {
    const shareData = {
      title: '八闽助业集市邀请您',
      desc: `${userInfo.nickname}邀请您加入八闽助业集市，一起助力乡村振兴`,
      link: `${window.location.origin}?inviteCode=${userInfo.inviteCode}`,
      imgUrl: 'https://your-domain.com/invite-bg.png'
    }

    await this.configShare(shareData)
  }
}
```

### 微信支付集成

#### 支付配置
```javascript
// utils/wechat-pay.js
export class WechatPay {
  // 发起微信支付
  static async pay(orderInfo) {
    if (!wechatSDK.isReady) {
      await wechatSDK.init()
    }

    try {
      // 获取支付参数
      const payParams = await this.getPayParams(orderInfo)
      
      return new Promise((resolve, reject) => {
        wx.chooseWXPay({
          timestamp: payParams.timestamp,
          nonceStr: payParams.nonceStr,
          package: payParams.package,
          signType: payParams.signType,
          paySign: payParams.paySign,
          success: (res) => {
            console.log('微信支付成功:', res)
            resolve(res)
          },
          fail: (error) => {
            console.error('微信支付失败:', error)
            reject(error)
          },
          cancel: () => {
            console.log('用户取消支付')
            reject(new Error('用户取消支付'))
          }
        })
      })
    } catch (error) {
      console.error('获取支付参数失败:', error)
      throw error
    }
  }

  // 获取支付参数
  static async getPayParams(orderInfo) {
    const response = await uni.request({
      url: '/after/pay/wechat/prepare',
      method: 'POST',
      data: {
        orderId: orderInfo.orderId,
        amount: orderInfo.amount,
        description: orderInfo.description
      }
    })

    if (response.data.success) {
      return response.data.result
    } else {
      throw new Error(response.data.message)
    }
  }

  // 查询支付结果
  static async queryPayResult(orderId) {
    const response = await uni.request({
      url: '/after/pay/query',
      method: 'GET',
      data: { orderId }
    })

    if (response.data.success) {
      return response.data.result
    } else {
      throw new Error(response.data.message)
    }
  }
}
```

## 企业微信集成

### 企业微信SDK配置
```javascript
// utils/work-wechat.js
import { wwLogin } from '@wecom/jssdk'

export class WorkWechatSDK {
  constructor() {
    this.isReady = false
    this.config = {
      corpId: 'your-corp-id',
      agentId: 'your-agent-id'
    }
  }

  // 初始化企业微信SDK
  async init() {
    try {
      const configData = await this.getWorkWechatConfig()
      
      wwLogin.config({
        corpId: configData.corpId,
        timestamp: configData.timestamp,
        nonceStr: configData.nonceStr,
        signature: configData.signature,
        jsApiList: [
          'login',
          'getUserInfo',
          'selectExternalContact'
        ]
      })

      return new Promise((resolve, reject) => {
        wwLogin.ready(() => {
          this.isReady = true
          console.log('企业微信SDK初始化成功')
          resolve(true)
        })

        wwLogin.error((error) => {
          console.error('企业微信SDK初始化失败:', error)
          reject(error)
        })
      })
    } catch (error) {
      console.error('企业微信SDK初始化失败:', error)
      throw error
    }
  }

  // 获取企业微信配置
  async getWorkWechatConfig() {
    const url = encodeURIComponent(window.location.href.split('#')[0])
    const response = await uni.request({
      url: '/front/work-wechat/config',
      method: 'POST',
      data: { url }
    })

    if (response.data.success) {
      return response.data.result
    } else {
      throw new Error(response.data.message)
    }
  }

  // 企业微信登录
  async login() {
    if (!this.isReady) {
      await this.init()
    }

    return new Promise((resolve, reject) => {
      wwLogin.login({
        success: (res) => {
          console.log('企业微信登录成功:', res)
          resolve(res)
        },
        fail: (error) => {
          console.error('企业微信登录失败:', error)
          reject(error)
        }
      })
    })
  }
}
```

## H5特定功能实现

### 页面适配
```javascript
// utils/h5-adapter.js
export class H5Adapter {
  // 设置页面标题
  static setTitle(title) {
    document.title = title
    
    // 微信环境下的标题设置
    if (EnvironmentDetector.isWechat()) {
      const iframe = document.createElement('iframe')
      iframe.style.display = 'none'
      iframe.src = '/favicon.ico'
      iframe.onload = () => {
        setTimeout(() => {
          document.body.removeChild(iframe)
        }, 0)
      }
      document.body.appendChild(iframe)
    }
  }

  // 禁用微信下拉刷新
  static disablePullRefresh() {
    document.addEventListener('touchmove', (e) => {
      if (e.touches.length > 1) {
        e.preventDefault()
      }
    }, { passive: false })

    let startY = 0
    document.addEventListener('touchstart', (e) => {
      startY = e.touches[0].clientY
    })

    document.addEventListener('touchmove', (e) => {
      const currentY = e.touches[0].clientY
      if (currentY > startY && document.documentElement.scrollTop === 0) {
        e.preventDefault()
      }
    }, { passive: false })
  }

  // 设置状态栏样式
  static setStatusBarStyle(style = 'dark') {
    const metaTag = document.querySelector('meta[name="theme-color"]')
    if (metaTag) {
      metaTag.content = style === 'dark' ? '#000000' : '#ffffff'
    }
  }
}
```

### 错误处理
```javascript
// utils/h5-error-handler.js
export class H5ErrorHandler {
  static init() {
    // 全局错误捕获
    window.addEventListener('error', (event) => {
      console.error('H5页面错误:', event.error)
      this.reportError({
        type: 'javascript',
        message: event.error?.message || '未知错误',
        stack: event.error?.stack,
        url: window.location.href,
        userAgent: navigator.userAgent
      })
    })

    // Promise错误捕获
    window.addEventListener('unhandledrejection', (event) => {
      console.error('Promise错误:', event.reason)
      this.reportError({
        type: 'promise',
        message: event.reason?.message || '未知Promise错误',
        url: window.location.href,
        userAgent: navigator.userAgent
      })
    })

    // 微信SDK错误处理
    if (typeof wx !== 'undefined') {
      wx.error((res) => {
        console.error('微信SDK错误:', res)
        this.reportError({
          type: 'wechat-sdk',
          message: res.errMsg || '微信SDK错误',
          url: window.location.href,
          userAgent: navigator.userAgent
        })
      })
    }
  }

  // 错误上报
  static async reportError(errorInfo) {
    try {
      await uni.request({
        url: '/front/error/report',
        method: 'POST',
        data: {
          ...errorInfo,
          timestamp: Date.now(),
          environment: EnvironmentDetector.getEnvironmentType()
        }
      })
    } catch (error) {
      console.error('错误上报失败:', error)
    }
  }
}
```

## 性能优化

### 资源预加载
```javascript
// utils/preload.js
export class ResourcePreloader {
  // 预加载微信SDK
  static preloadWechatSDK() {
    const script = document.createElement('script')
    script.src = 'https://res.wx.qq.com/open/js/jweixin-1.6.0.js'
    script.async = true
    document.head.appendChild(script)
  }

  // 预加载关键资源
  static preloadCriticalResources() {
    const resources = [
      '/static/images/logo.png',
      '/static/images/default-avatar.png',
      '/static/images/placeholder.png'
    ]

    resources.forEach(url => {
      const link = document.createElement('link')
      link.rel = 'preload'
      link.as = 'image'
      link.href = url
      document.head.appendChild(link)
    })
  }
}
```

### 缓存策略
```javascript
// utils/cache.js
export class H5Cache {
  // 设置缓存
  static set(key, value, expireTime = 3600000) { // 默认1小时
    const data = {
      value,
      expireTime: Date.now() + expireTime
    }
    localStorage.setItem(`h5_${key}`, JSON.stringify(data))
  }

  // 获取缓存
  static get(key) {
    const item = localStorage.getItem(`h5_${key}`)
    if (!item) return null

    const data = JSON.parse(item)
    if (Date.now() > data.expireTime) {
      localStorage.removeItem(`h5_${key}`)
      return null
    }

    return data.value
  }

  // 清除过期缓存
  static clearExpired() {
    const keys = Object.keys(localStorage)
    keys.forEach(key => {
      if (key.startsWith('h5_')) {
        const item = localStorage.getItem(key)
        if (item) {
          const data = JSON.parse(item)
          if (Date.now() > data.expireTime) {
            localStorage.removeItem(key)
          }
        }
      }
    })
  }
}
```

## 安全措施

### 数据安全
```javascript
// utils/security.js
export class H5Security {
  // URL参数验证
  static validateUrlParams() {
    const urlParams = new URLSearchParams(window.location.search)
    const dangerousParams = ['script', 'javascript:', 'data:', 'vbscript:']
    
    for (const [key, value] of urlParams) {
      const lowerValue = value.toLowerCase()
      if (dangerousParams.some(param => lowerValue.includes(param))) {
        console.warn('检测到危险参数:', key, value)
        return false
      }
    }
    return true
  }

  // 防止XSS攻击
  static escapeHtml(text) {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  // 验证来源域名
  static validateOrigin() {
    const allowedOrigins = [
      'https://your-domain.com',
      'https://mp.weixin.qq.com'
    ]
    
    const referrer = document.referrer
    if (referrer && !allowedOrigins.some(origin => referrer.startsWith(origin))) {
      console.warn('非法来源访问:', referrer)
      return false
    }
    return true
  }
}
```

本文档提供了H5端微信集成的完整架构方案，确保在微信生态中的稳定运行和良好用户体验。
