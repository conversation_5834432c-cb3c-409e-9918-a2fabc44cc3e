# 八闽助业集市管理后台路由配置规范

## 🛣️ 路由架构概述

### 路由管理策略
```
┌─────────────────────────────────────────────────────────────────┐
│                        路由管理架构                              │
├─────────────────────────────────────────────────────────────────┤
│  静态路由 (constantRouterMap)  → 无需权限的基础路由              │
│  动态路由 (asyncRouterMap)     → 需要权限控制的业务路由          │
│  权限过滤 (filterAsyncRouter)  → 根据用户权限过滤路由            │
│  路由守卫 (beforeEach)         → 路由跳转前的权限验证            │
└─────────────────────────────────────────────────────────────────┘
```

### 路由配置原则
- **权限控制**: 基于角色和权限的动态路由
- **懒加载**: 路由组件按需加载，提升性能
- **嵌套路由**: 合理的路由层级结构
- **路由守卫**: 完善的权限验证机制

## 📋 静态路由配置

### 基础路由定义
```javascript
// src/config/router.config.js
import { UserLayout, BasicLayout, RouteView, BlankLayout, PageView } from '@/layouts'

/**
 * 基础路由
 * 不需要权限控制的路由
 */
export const constantRouterMap = [
  {
    path: '/user',
    component: UserLayout,
    redirect: '/user/login',
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'login',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Login')
      },
      {
        path: 'register',
        name: 'register',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Register')
      },
      {
        path: 'register-result',
        name: 'registerResult',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/RegisterResult')
      },
      {
        path: 'recover',
        name: 'recover',
        component: () => import(/* webpackChunkName: "user" */ '@/views/user/Recover')
      }
    ]
  },
  {
    path: '/404',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/404')
  },
  {
    path: '/403',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/403')
  },
  {
    path: '/500',
    component: () => import(/* webpackChunkName: "fail" */ '@/views/exception/500')
  }
]
```

## 🔐 动态路由配置

### 权限路由定义
```javascript
/**
 * 动态路由
 * 需要根据用户权限动态添加的路由
 */
export const asyncRouterMap = [
  {
    path: '/',
    name: 'index',
    component: BasicLayout,
    meta: { title: '首页' },
    redirect: '/dashboard/analysis',
    children: [
      // 仪表盘
      {
        path: '/dashboard',
        name: 'dashboard',
        redirect: '/dashboard/analysis',
        component: RouteView,
        meta: {
          title: '仪表盘',
          keepAlive: true,
          icon: 'dashboard',
          permission: ['dashboard']
        },
        children: [
          {
            path: '/dashboard/analysis',
            name: 'Analysis',
            component: () => import('@/views/dashboard/Analysis'),
            meta: {
              title: '分析页',
              keepAlive: false,
              permission: ['dashboard']
            }
          },
          {
            path: '/dashboard/monitor',
            name: 'Monitor',
            component: () => import('@/views/dashboard/Monitor'),
            meta: {
              title: '监控页',
              keepAlive: true,
              permission: ['dashboard']
            }
          }
        ]
      },

      // 用户管理
      {
        path: '/user',
        redirect: '/user/user-list',
        component: PageView,
        meta: {
          title: '用户管理',
          icon: 'user',
          permission: ['user']
        },
        children: [
          {
            path: '/user/user-list',
            name: 'UserList',
            component: () => import('@/views/user/UserList'),
            meta: {
              title: '用户列表',
              keepAlive: true,
              permission: ['user:list']
            }
          },
          {
            path: '/user/user-detail/:id',
            name: 'UserDetail',
            component: () => import('@/views/user/UserDetail'),
            meta: {
              title: '用户详情',
              keepAlive: false,
              permission: ['user:view'],
              hidden: true
            }
          }
        ]
      },

      // 商品管理
      {
        path: '/product',
        redirect: '/product/product-list',
        component: PageView,
        meta: {
          title: '商品管理',
          icon: 'shopping',
          permission: ['product']
        },
        children: [
          {
            path: '/product/category',
            name: 'ProductCategory',
            component: () => import('@/views/product/Category'),
            meta: {
              title: '商品分类',
              keepAlive: true,
              permission: ['product:category']
            }
          },
          {
            path: '/product/product-list',
            name: 'ProductList',
            component: () => import('@/views/product/ProductList'),
            meta: {
              title: '商品列表',
              keepAlive: true,
              permission: ['product:list']
            }
          },
          {
            path: '/product/product-add',
            name: 'ProductAdd',
            component: () => import('@/views/product/ProductForm'),
            meta: {
              title: '添加商品',
              keepAlive: false,
              permission: ['product:add'],
              hidden: true
            }
          },
          {
            path: '/product/product-edit/:id',
            name: 'ProductEdit',
            component: () => import('@/views/product/ProductForm'),
            meta: {
              title: '编辑商品',
              keepAlive: false,
              permission: ['product:edit'],
              hidden: true
            }
          }
        ]
      },

      // 订单管理
      {
        path: '/order',
        redirect: '/order/order-list',
        component: PageView,
        meta: {
          title: '订单管理',
          icon: 'shopping-cart',
          permission: ['order']
        },
        children: [
          {
            path: '/order/order-list',
            name: 'OrderList',
            component: () => import('@/views/order/OrderList'),
            meta: {
              title: '订单列表',
              keepAlive: true,
              permission: ['order:list']
            }
          },
          {
            path: '/order/order-detail/:id',
            name: 'OrderDetail',
            component: () => import('@/views/order/OrderDetail'),
            meta: {
              title: '订单详情',
              keepAlive: false,
              permission: ['order:view'],
              hidden: true
            }
          }
        ]
      },

      // 系统管理
      {
        path: '/system',
        redirect: '/system/user',
        component: PageView,
        meta: {
          title: '系统管理',
          icon: 'setting',
          permission: ['system']
        },
        children: [
          {
            path: '/system/user',
            name: 'SystemUser',
            component: () => import('@/views/system/UserList'),
            meta: {
              title: '用户管理',
              keepAlive: true,
              permission: ['system:user']
            }
          },
          {
            path: '/system/role',
            name: 'SystemRole',
            component: () => import('@/views/system/RoleList'),
            meta: {
              title: '角色管理',
              keepAlive: true,
              permission: ['system:role']
            }
          },
          {
            path: '/system/permission',
            name: 'SystemPermission',
            component: () => import('@/views/system/PermissionList'),
            meta: {
              title: '权限管理',
              keepAlive: true,
              permission: ['system:permission']
            }
          },
          {
            path: '/system/dict',
            name: 'SystemDict',
            component: () => import('@/views/system/DictList'),
            meta: {
              title: '字典管理',
              keepAlive: true,
              permission: ['system:dict']
            }
          }
        ]
      }
    ]
  },
  {
    path: '*',
    redirect: '/404',
    hidden: true
  }
]
```

## 🔧 路由实例配置

### Vue Router配置
```javascript
// src/router/index.js
import Vue from 'vue'
import Router from 'vue-router'
import { constantRouterMap } from '@/config/router.config'

Vue.use(Router)

export default new Router({
  mode: 'history',
  base: process.env.BASE_URL,
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})
```

## 🛡️ 路由守卫配置

### 权限验证守卫
```javascript
// src/permission.js
import router from './router'
import store from './store'
import { ACCESS_TOKEN } from '@/store/mutation-types'
import { domTitle, setDocumentTitle } from '@/utils/domUtil'

const whiteList = ['login', 'register', 'registerResult'] // 白名单

router.beforeEach((to, from, next) => {
  // 设置页面标题
  to.meta && (typeof to.meta.title !== 'undefined' && setDocumentTitle(`${to.meta.title} - ${domTitle}`))
  
  // 获取token
  const token = Vue.ls.get(ACCESS_TOKEN)
  
  if (token) {
    // 已登录
    if (to.path === '/user/login') {
      // 已登录且要跳转的页面是登录页
      next({ path: '/dashboard/analysis' })
    } else {
      // 检查用户信息是否存在
      if (store.getters.roles.length === 0) {
        // 获取用户信息
        store.dispatch('GetInfo').then(res => {
          const roles = res.result && res.result.roles
          // 根据roles权限生成可访问的路由表
          store.dispatch('GenerateRoutes', { roles }).then(() => {
            // 动态添加可访问路由表
            router.addRoutes(store.getters.addRouters)
            // 请求带有 redirect 重定向时，登录自动重定向到该地址
            const redirect = decodeURIComponent(from.query.redirect || to.path)
            if (to.path === redirect) {
              // hack方法 确保addRoutes已完成 ,set the replace: true so the navigation will not leave a history record
              next({ ...to, replace: true })
            } else {
              // 跳转到目的路由
              next({ path: redirect })
            }
          })
        }).catch(() => {
          // 获取用户信息失败，清除token并跳转到登录页
          store.dispatch('Logout').then(() => {
            next({ path: '/user/login', query: { redirect: to.fullPath } })
          })
        })
      } else {
        next()
      }
    }
  } else {
    // 未登录
    if (whiteList.includes(to.name)) {
      // 在免登录白名单，直接进入
      next()
    } else {
      // 否则全部重定向到登录页
      next({ path: '/user/login', query: { redirect: to.fullPath } })
    }
  }
})

router.afterEach(() => {
  // 路由跳转完成后的处理
})
```

## 📱 路由元信息配置

### Meta字段说明
```javascript
const routeConfig = {
  path: '/example',
  name: 'Example',
  component: () => import('@/views/Example'),
  meta: {
    title: '页面标题',           // 页面标题，用于面包屑和标签页
    icon: 'icon-name',          // 菜单图标
    keepAlive: true,            // 是否缓存页面
    permission: ['example'],     // 访问权限，数组格式
    roles: ['admin'],           // 访问角色，数组格式
    hidden: false,              // 是否在菜单中隐藏
    hideHeader: false,          // 是否隐藏页面头部
    hideChildren: false,        // 是否隐藏子菜单
    target: '_blank',           // 链接跳转方式
    affix: false,               // 是否固定在标签页
    noCache: false,             // 是否不缓存
    breadcrumb: true,           // 是否显示面包屑
    activeMenu: '/example/list'  // 高亮菜单项
  }
}
```

## 🔄 动态路由添加

### 权限路由过滤
```javascript
// src/store/modules/permission.js
/**
 * 过滤账户是否拥有某一个权限，并将菜单从加载列表移除
 */
function hasPermission(permission, route) {
  if (route.meta && route.meta.permission) {
    let flag = false
    for (let i = 0, len = permission.length; i < len; i++) {
      flag = route.meta.permission.includes(permission[i])
      if (flag) {
        return true
      }
    }
    return false
  }
  return true
}

/**
 * 递归过滤异步路由表，返回符合用户角色权限的路由表
 */
function filterAsyncRouter(routerMap, roles) {
  const accessedRouters = routerMap.filter(route => {
    if (hasPermission(roles.permissionList, route)) {
      if (route.children && route.children.length) {
        route.children = filterAsyncRouter(route.children, roles)
      }
      return true
    }
    return false
  })
  return accessedRouters
}

const permission = {
  state: {
    routers: constantRouterMap,
    addRouters: []
  },
  mutations: {
    SET_ROUTERS: (state, routers) => {
      state.addRouters = routers
      state.routers = constantRouterMap.concat(routers)
    }
  },
  actions: {
    GenerateRoutes({ commit }, data) {
      return new Promise(resolve => {
        const { roles } = data
        const accessedRouters = filterAsyncRouter(asyncRouterMap, roles)
        commit('SET_ROUTERS', accessedRouters)
        resolve()
      })
    }
  }
}
```

## 🍞 面包屑导航

### 面包屑组件
```vue
<template>
  <a-breadcrumb class="breadcrumb">
    <a-breadcrumb-item v-for="item in breadcrumbList" :key="item.path">
      <router-link v-if="item.path && item.path !== $route.path" :to="item.path">
        {{ item.meta.title }}
      </router-link>
      <span v-else>{{ item.meta.title }}</span>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script>
export default {
  name: 'Breadcrumb',
  data() {
    return {
      breadcrumbList: []
    }
  },
  watch: {
    $route: {
      handler(route) {
        this.getBreadcrumb()
      },
      immediate: true
    }
  },
  methods: {
    getBreadcrumb() {
      // 过滤掉不显示面包屑的路由
      let matched = this.$route.matched.filter(item => item.meta && item.meta.title && item.meta.breadcrumb !== false)
      
      // 添加首页
      const first = matched[0]
      if (!this.isDashboard(first)) {
        matched = [{ path: '/dashboard/analysis', meta: { title: '首页' } }].concat(matched)
      }
      
      this.breadcrumbList = matched.filter((item, index, arr) => {
        return item.meta && item.meta.title && index === arr.length - 1 || item.redirect !== 'noRedirect'
      })
    },
    
    isDashboard(route) {
      const name = route && route.name
      if (!name) {
        return false
      }
      return name.trim().toLocaleLowerCase() === 'Dashboard'.toLocaleLowerCase()
    }
  }
}
</script>
```

## 📋 路由配置最佳实践

### 1. 路由命名规范
- 使用PascalCase命名路由name
- 路由path使用kebab-case
- 保持路由名称与组件名称一致

### 2. 组件懒加载
- 使用动态import进行代码分割
- 合理使用webpackChunkName分组
- 避免首屏加载过多资源

### 3. 权限控制
- 在路由meta中定义权限
- 使用路由守卫进行权限验证
- 支持角色和权限双重控制

### 4. 路由缓存
- 合理使用keepAlive缓存页面
- 避免缓存包含敏感信息的页面
- 提供清除缓存的机制

### 5. 错误处理
- 配置404、403等错误页面
- 处理路由跳转异常
- 提供友好的错误提示

---

**文档说明**: 本文档为八闽助业集市管理后台路由配置规范，所有路由配置必须严格遵循此规范。
