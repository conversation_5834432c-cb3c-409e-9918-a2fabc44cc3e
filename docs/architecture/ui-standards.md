# 八闽助业集市商家端小程序UI设计规范

## 🎨 商家端设计系统

### 设计理念
```
┌─────────────────────────────────────────────────────────────────┐
│                    商家端设计原则                                │
├─────────────────────────────────────────────────────────────────┤
│  专业性      → 体现商家管理的专业性和权威性                      │
│  效率性      → 优化操作流程，提升工作效率                        │
│  数据化      → 突出数据展示和分析功能                           │
│  一致性      → 与C端保持视觉一致性，降低学习成本                 │
│  移动优先    → 针对移动端操作优化界面布局                        │
└─────────────────────────────────────────────────────────────────┘
```

### 商家端色彩系统
```scss
// merchant-design-tokens.scss
:root {
  /* ========== 继承C端基础色彩 ========== */
  --primary-color: #667eea;
  --primary-light: #8fa4f3;
  --primary-dark: #4c63d2;
  
  /* ========== 商家端专用色彩 ========== */
  --merchant-primary: #667eea;
  --merchant-secondary: #f0f2ff;
  
  /* ========== 业务功能色彩 ========== */
  --revenue-color: #52c41a;
  --revenue-light: rgba(82, 196, 26, 0.1);
  --order-color: #1890ff;
  --order-light: rgba(24, 144, 255, 0.1);
  --product-color: #722ed1;
  --product-light: rgba(114, 46, 209, 0.1);
  --customer-color: #eb2f96;
  --customer-light: rgba(235, 47, 150, 0.1);
  
  /* ========== 状态色彩系统 ========== */
  --status-pending: #faad14;
  --status-pending-bg: rgba(250, 173, 20, 0.1);
  --status-processing: #1890ff;
  --status-processing-bg: rgba(24, 144, 255, 0.1);
  --status-completed: #52c41a;
  --status-completed-bg: rgba(82, 196, 26, 0.1);
  --status-cancelled: #f5222d;
  --status-cancelled-bg: rgba(245, 34, 45, 0.1);
  --status-refund: #fa8c16;
  --status-refund-bg: rgba(250, 140, 22, 0.1);
  
  /* ========== 数据可视化色彩 ========== */
  --chart-color-1: #667eea;
  --chart-color-2: #52c41a;
  --chart-color-3: #faad14;
  --chart-color-4: #f5222d;
  --chart-color-5: #722ed1;
  --chart-color-6: #eb2f96;
  
  /* ========== 继承基础设计令牌 ========== */
  --border-radius-card: 16px;
  --border-radius-component: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-elevated: 0 4px 16px rgba(0, 0, 0, 0.1);
}
```

## 📊 数据展示组件

### 统计卡片组件
```vue
<template>
  <view class="merchant-stats-card" :style="cardStyle">
    <view class="stats-header">
      <view class="stats-icon" :style="iconStyle">
        <uv-icon :name="icon" :size="iconSize" color="white" />
      </view>
      <view class="stats-trend" v-if="trend">
        <text class="trend-text" :style="trendTextStyle">{{ trend }}</text>
        <uv-icon 
          :name="trendIcon" 
          size="12" 
          :color="trendColor"
        />
      </view>
    </view>
    
    <view class="stats-content">
      <text class="stats-value">{{ formattedValue }}</text>
      <text class="stats-title">{{ title }}</text>
    </view>
    
    <view class="stats-footer" v-if="subtitle">
      <text class="stats-subtitle">{{ subtitle }}</text>
    </view>
    
    <view class="stats-action" v-if="actionText" @click="handleAction">
      <text class="action-text">{{ actionText }}</text>
      <uv-icon name="arrow-right" size="14" color="var(--text-tertiary)" />
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: String,
  value: [String, Number],
  subtitle: String,
  trend: String,
  icon: String,
  color: {
    type: String,
    default: 'var(--primary-color)'
  },
  actionText: String,
  type: {
    type: String,
    default: 'default',
    validator: value => ['default', 'currency', 'percentage', 'number'].includes(value)
  }
})

const emit = defineEmits(['action'])

// 计算属性
const cardStyle = computed(() => ({
  background: `linear-gradient(135deg, ${props.color} 0%, ${props.color}dd 100%)`
}))

const iconStyle = computed(() => ({
  backgroundColor: 'rgba(255, 255, 255, 0.2)',
  backdropFilter: 'blur(10px)'
}))

const iconSize = computed(() => 24)

const formattedValue = computed(() => {
  if (!props.value) return '0'
  
  const value = typeof props.value === 'string' ? parseFloat(props.value) : props.value
  
  switch (props.type) {
    case 'currency':
      return `¥${value.toLocaleString()}`
    case 'percentage':
      return `${value}%`
    case 'number':
      return value.toLocaleString()
    default:
      return props.value
  }
})

const trendIcon = computed(() => {
  if (!props.trend) return ''
  const isPositive = props.trend.startsWith('+')
  return isPositive ? 'trending-up' : 'trending-down'
})

const trendColor = computed(() => {
  if (!props.trend) return 'rgba(255, 255, 255, 0.7)'
  const isPositive = props.trend.startsWith('+')
  return isPositive ? '#52c41a' : '#f5222d'
})

const trendTextStyle = computed(() => ({
  color: trendColor.value
}))

// 事件处理
const handleAction = () => {
  emit('action')
}
</script>

<style lang="scss" scoped>
.merchant-stats-card {
  border-radius: var(--border-radius-card);
  padding: var(--spacing-lg);
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-elevated);
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(30px, -30px);
  }
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--spacing-md);
  
  .stats-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius-component);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .stats-trend {
    display: flex;
    align-items: center;
    gap: 4px;
    
    .trend-text {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-semibold);
    }
  }
}

.stats-content {
  margin-bottom: var(--spacing-sm);
  
  .stats-value {
    font-size: 28px;
    font-weight: var(--font-weight-bold);
    line-height: 1.2;
    display: block;
    margin-bottom: var(--spacing-xs);
  }
  
  .stats-title {
    font-size: var(--font-size-md);
    opacity: 0.9;
    display: block;
  }
}

.stats-footer {
  margin-bottom: var(--spacing-sm);
  
  .stats-subtitle {
    font-size: var(--font-size-sm);
    opacity: 0.8;
  }
}

.stats-action {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: var(--spacing-sm);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  
  .action-text {
    font-size: var(--font-size-sm);
    opacity: 0.9;
  }
}
</style>
```

### 图表容器组件
```vue
<template>
  <view class="chart-container">
    <view class="chart-header">
      <text class="chart-title">{{ title }}</text>
      <view class="chart-controls" v-if="$slots.controls">
        <slot name="controls"></slot>
      </view>
    </view>
    
    <view class="chart-content" :style="contentStyle">
      <slot></slot>
    </view>
    
    <view class="chart-legend" v-if="showLegend && legendData.length">
      <view 
        v-for="item in legendData" 
        :key="item.key"
        class="legend-item"
      >
        <view class="legend-color" :style="{ backgroundColor: item.color }"></view>
        <text class="legend-label">{{ item.label }}</text>
        <text class="legend-value">{{ item.value }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: String,
  height: {
    type: [String, Number],
    default: 200
  },
  showLegend: {
    type: Boolean,
    default: false
  },
  legendData: {
    type: Array,
    default: () => []
  }
})

const contentStyle = computed(() => ({
  height: typeof props.height === 'number' ? `${props.height}px` : props.height
}))
</script>

<style lang="scss" scoped>
.chart-container {
  background: var(--background-primary);
  border-radius: var(--border-radius-card);
  box-shadow: var(--shadow-card);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color-light);
  
  .chart-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
  }
}

.chart-content {
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  justify-content: center;
}

.chart-legend {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color-light);
  background: var(--background-secondary);
  
  .legend-item {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-xs);
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .legend-color {
      width: 12px;
      height: 12px;
      border-radius: 2px;
      margin-right: var(--spacing-sm);
    }
    
    .legend-label {
      flex: 1;
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
    }
    
    .legend-value {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--text-primary);
    }
  }
}
</style>
```

## 📋 列表和表格组件

### 商家端数据列表
```vue
<template>
  <view class="merchant-data-list">
    <!-- 列表头部 -->
    <view class="list-header" v-if="showHeader">
      <text class="list-title">{{ title }}</text>
      <view class="list-actions">
        <slot name="actions"></slot>
      </view>
    </view>
    
    <!-- 筛选器 -->
    <view class="list-filters" v-if="filters.length">
      <scroll-view scroll-x class="filter-scroll">
        <view class="filter-items">
          <view
            v-for="filter in filters"
            :key="filter.key"
            class="filter-item"
            :class="{ active: activeFilter === filter.key }"
            @click="handleFilterChange(filter.key)"
          >
            <text class="filter-text">{{ filter.label }}</text>
            <view class="filter-badge" v-if="filter.count">
              <text class="badge-text">{{ filter.count }}</text>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 列表内容 -->
    <uv-pull-refresh v-model="refreshing" @refresh="onRefresh">
      <uv-list @scrolltolower="onLoadMore">
        <view
          v-for="(item, index) in dataList"
          :key="getItemKey(item, index)"
          class="list-item"
          @click="handleItemClick(item, index)"
        >
          <slot :item="item" :index="index">
            <!-- 默认列表项模板 -->
            <view class="default-item">
              <view class="item-main">
                <text class="item-title">{{ item.title || item.name }}</text>
                <text class="item-subtitle" v-if="item.subtitle">{{ item.subtitle }}</text>
              </view>
              <view class="item-extra">
                <text class="item-value">{{ item.value }}</text>
                <uv-icon name="arrow-right" size="16" color="var(--text-tertiary)" />
              </view>
            </view>
          </slot>
        </view>
        
        <!-- 加载更多 -->
        <uv-load-more :status="loadStatus" />
      </uv-list>
    </uv-pull-refresh>
    
    <!-- 空状态 -->
    <view class="empty-state" v-if="!loading && dataList.length === 0">
      <image src="/static/images/empty.png" class="empty-image" />
      <text class="empty-text">{{ emptyText }}</text>
      <uv-button v-if="emptyAction" type="primary" size="small" @click="handleEmptyAction">
        {{ emptyAction }}
      </uv-button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'

const props = defineProps({
  title: String,
  dataList: {
    type: Array,
    default: () => []
  },
  loading: Boolean,
  refreshing: Boolean,
  hasMore: Boolean,
  showHeader: {
    type: Boolean,
    default: true
  },
  filters: {
    type: Array,
    default: () => []
  },
  emptyText: {
    type: String,
    default: '暂无数据'
  },
  emptyAction: String,
  itemKey: {
    type: String,
    default: 'id'
  }
})

const emit = defineEmits(['refresh', 'loadMore', 'itemClick', 'filterChange', 'emptyAction'])

// 响应式数据
const activeFilter = ref('')

// 计算属性
const loadStatus = computed(() => {
  if (props.loading) return 'loading'
  if (!props.hasMore) return 'nomore'
  return 'loadmore'
})

// 方法
const getItemKey = (item, index) => {
  return item[props.itemKey] || index
}

const handleFilterChange = (filterKey) => {
  activeFilter.value = filterKey
  emit('filterChange', filterKey)
}

const handleItemClick = (item, index) => {
  emit('itemClick', item, index)
}

const onRefresh = () => {
  emit('refresh')
}

const onLoadMore = () => {
  emit('loadMore')
}

const handleEmptyAction = () => {
  emit('emptyAction')
}
</script>

<style lang="scss" scoped>
.merchant-data-list {
  background: var(--background-secondary);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--background-primary);
  border-bottom: 1px solid var(--border-color-light);
  
  .list-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
  }
}

.list-filters {
  background: var(--background-primary);
  border-bottom: 1px solid var(--border-color-light);
  
  .filter-scroll {
    white-space: nowrap;
  }
  
  .filter-items {
    display: flex;
    padding: var(--spacing-sm) var(--spacing-md);
    gap: var(--spacing-sm);
  }
  
  .filter-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-component);
    background: var(--background-secondary);
    border: 1px solid var(--border-color);
    transition: all var(--transition-fast);
    
    &.active {
      background: var(--primary-color);
      border-color: var(--primary-color);
      
      .filter-text {
        color: white;
      }
      
      .badge-text {
        color: var(--primary-color);
        background: white;
      }
    }
    
    .filter-text {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      white-space: nowrap;
    }
    
    .filter-badge {
      margin-left: var(--spacing-xs);
      
      .badge-text {
        font-size: var(--font-size-xs);
        color: white;
        background: var(--primary-color);
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 16px;
        text-align: center;
      }
    }
  }
}

.list-item {
  background: var(--background-primary);
  margin: 0 var(--spacing-md) var(--spacing-sm);
  border-radius: var(--border-radius-component);
  box-shadow: var(--shadow-card);
  transition: transform var(--transition-fast);
  
  &:first-child {
    margin-top: var(--spacing-md);
  }
  
  &:active {
    transform: scale(0.98);
  }
}

.default-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  
  .item-main {
    flex: 1;
    min-width: 0;
    
    .item-title {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      color: var(--text-primary);
      display: block;
      margin-bottom: var(--spacing-xs);
    }
    
    .item-subtitle {
      font-size: var(--font-size-sm);
      color: var(--text-secondary);
      display: block;
    }
  }
  
  .item-extra {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    
    .item-value {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      color: var(--text-primary);
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
  
  .empty-image {
    width: 120px;
    height: 120px;
    margin-bottom: var(--spacing-lg);
    opacity: 0.6;
  }
  
  .empty-text {
    font-size: var(--font-size-md);
    color: var(--text-tertiary);
    margin-bottom: var(--spacing-lg);
  }
}
</style>
```

## 🎨 状态指示器组件

### 状态标签组件
```vue
<template>
  <view class="status-tag" :class="statusClass" :style="statusStyle">
    <view class="status-dot" v-if="showDot"></view>
    <text class="status-text">{{ text }}</text>
  </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  status: {
    type: [String, Number],
    required: true
  },
  type: {
    type: String,
    default: 'order',
    validator: value => ['order', 'product', 'payment', 'merchant'].includes(value)
  },
  showDot: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'default',
    validator: value => ['small', 'default', 'large'].includes(value)
  }
})

// 状态配置映射
const statusConfig = {
  order: {
    0: { text: '待付款', color: 'var(--status-pending)', bg: 'var(--status-pending-bg)' },
    1: { text: '待发货', color: 'var(--status-processing)', bg: 'var(--status-processing-bg)' },
    2: { text: '已发货', color: 'var(--status-processing)', bg: 'var(--status-processing-bg)' },
    3: { text: '已完成', color: 'var(--status-completed)', bg: 'var(--status-completed-bg)' },
    4: { text: '已取消', color: 'var(--status-cancelled)', bg: 'var(--status-cancelled-bg)' },
    5: { text: '退款中', color: 'var(--status-refund)', bg: 'var(--status-refund-bg)' },
    6: { text: '已退款', color: 'var(--status-refund)', bg: 'var(--status-refund-bg)' }
  },
  product: {
    0: { text: '草稿', color: 'var(--text-tertiary)', bg: 'var(--background-tertiary)' },
    1: { text: '在售', color: 'var(--status-completed)', bg: 'var(--status-completed-bg)' },
    2: { text: '下架', color: 'var(--status-cancelled)', bg: 'var(--status-cancelled-bg)' },
    3: { text: '售罄', color: 'var(--status-pending)', bg: 'var(--status-pending-bg)' }
  },
  payment: {
    0: { text: '未支付', color: 'var(--status-pending)', bg: 'var(--status-pending-bg)' },
    1: { text: '已支付', color: 'var(--status-completed)', bg: 'var(--status-completed-bg)' },
    2: { text: '支付失败', color: 'var(--status-cancelled)', bg: 'var(--status-cancelled-bg)' }
  },
  merchant: {
    0: { text: '待审核', color: 'var(--status-pending)', bg: 'var(--status-pending-bg)' },
    1: { text: '正常营业', color: 'var(--status-completed)', bg: 'var(--status-completed-bg)' },
    2: { text: '暂停营业', color: 'var(--status-pending)', bg: 'var(--status-pending-bg)' },
    3: { text: '已关闭', color: 'var(--status-cancelled)', bg: 'var(--status-cancelled-bg)' }
  }
}

// 计算属性
const currentConfig = computed(() => {
  const config = statusConfig[props.type]
  return config[props.status] || { text: '未知状态', color: 'var(--text-tertiary)', bg: 'var(--background-tertiary)' }
})

const text = computed(() => currentConfig.value.text)

const statusClass = computed(() => [
  `status-tag-${props.size}`,
  { 'status-tag-dot': props.showDot }
])

const statusStyle = computed(() => ({
  color: currentConfig.value.color,
  backgroundColor: currentConfig.value.bg,
  borderColor: currentConfig.value.color
}))
</script>

<style lang="scss" scoped>
.status-tag {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: var(--border-radius-mini);
  border: 1px solid transparent;
  font-weight: var(--font-weight-medium);
  
  &.status-tag-small {
    padding: 2px 6px;
    font-size: var(--font-size-xs);
  }
  
  &.status-tag-default {
    padding: 4px 8px;
    font-size: var(--font-size-sm);
  }
  
  &.status-tag-large {
    padding: 6px 12px;
    font-size: var(--font-size-md);
  }
  
  &.status-tag-dot {
    .status-dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: currentColor;
      margin-right: 4px;
    }
  }
  
  .status-text {
    color: inherit;
  }
}
</style>
```

## 📋 商家端UI设计最佳实践

### 1. 数据可视化
- 使用图表展示业务数据趋势
- 提供多维度的数据分析视图
- 支持数据筛选和时间范围选择
- 使用颜色编码区分不同数据类型

### 2. 操作效率优化
- 提供批量操作功能
- 使用快捷操作按钮
- 支持键盘快捷键
- 优化表单填写流程

### 3. 状态管理
- 清晰的状态指示器
- 实时状态更新
- 状态变更历史记录
- 异常状态的醒目提示

### 4. 移动端适配
- 针对小屏幕优化布局
- 支持手势操作
- 合理的触摸目标大小
- 考虑单手操作便利性

### 5. 信息层次
- 突出重要信息
- 合理的信息分组
- 使用视觉层次引导用户
- 避免信息过载

---

**文档说明**: 本文档为八闽助业集市商家端小程序UI设计规范，专注于商家业务场景的界面设计和用户体验优化。
