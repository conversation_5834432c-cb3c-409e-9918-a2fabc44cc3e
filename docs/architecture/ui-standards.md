# 八闽助业集市C端小程序UI设计规范

## 🎨 现代卡片式设计系统

### 设计理念
```
┌─────────────────────────────────────────────────────────────────┐
│                    现代卡片式设计原则                            │
├─────────────────────────────────────────────────────────────────┤
│  简洁性      → 去除不必要的装饰，突出内容本身                    │
│  层次感      → 通过卡片、阴影、圆角营造空间层次                  │
│  一致性      → 统一的设计语言和交互模式                         │
│  响应式      → 适配不同屏幕尺寸和设备                           │
│  可访问性    → 良好的对比度和可读性                             │
└─────────────────────────────────────────────────────────────────┘
```

### 设计令牌系统
```scss
// design-tokens.scss
:root {
  /* ========== 品牌色彩 ========== */
  --primary-color: #667eea;
  --primary-light: #8fa4f3;
  --primary-dark: #4c63d2;
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* ========== 功能色彩 ========== */
  --success-color: #07c160;
  --success-light: #39d374;
  --warning-color: #ff976a;
  --warning-light: #ffab7e;
  --error-color: #ee0a24;
  --error-light: #f1384e;
  --info-color: #1989fa;
  --info-light: #47a3ff;
  
  /* ========== 中性色彩 ========== */
  --text-primary: #323233;
  --text-secondary: #646566;
  --text-tertiary: #969799;
  --text-disabled: #c8c9cc;
  --text-white: #ffffff;
  
  --background-primary: #ffffff;
  --background-secondary: #f7f8fa;
  --background-tertiary: #ebedf0;
  --background-overlay: rgba(0, 0, 0, 0.7);
  
  --border-color: #ebedf0;
  --border-color-light: #f2f3f5;
  --divider-color: #ebedf0;
  
  /* ========== 圆角系统 ========== */
  --border-radius-card: 16px;
  --border-radius-component: 12px;
  --border-radius-button: 8px;
  --border-radius-small: 6px;
  --border-radius-mini: 4px;
  
  /* ========== 间距系统 ========== */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
  
  /* ========== 阴影系统 ========== */
  --shadow-light: 0 2px 12px rgba(100, 101, 102, 0.08);
  --shadow-medium: 0 4px 20px rgba(100, 101, 102, 0.12);
  --shadow-heavy: 0 8px 32px rgba(100, 101, 102, 0.16);
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.06);
  
  /* ========== 字体系统 ========== */
  --font-size-xs: 10px;
  --font-size-sm: 12px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-xxl: 20px;
  --font-size-title: 24px;
  --font-size-hero: 32px;
  
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  
  /* ========== 动画系统 ========== */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.25s ease-out;
  --transition-slow: 0.35s ease-out;
  
  --animation-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --animation-ease: cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
```

## 🎛️ uv-ui组件使用规范

### 基础组件定制
```vue
<template>
  <view class="ui-showcase">
    <!-- 按钮组件 -->
    <view class="section">
      <text class="section-title">按钮组件</text>
      
      <!-- 主要按钮 -->
      <uv-button 
        type="primary" 
        :customStyle="primaryButtonStyle"
        @click="handlePrimaryAction"
      >
        主要操作
      </uv-button>
      
      <!-- 次要按钮 -->
      <uv-button 
        type="default" 
        :customStyle="secondaryButtonStyle"
        @click="handleSecondaryAction"
      >
        次要操作
      </uv-button>
      
      <!-- 渐变按钮 -->
      <uv-button 
        type="primary" 
        :customStyle="gradientButtonStyle"
        @click="handleGradientAction"
      >
        渐变按钮
      </uv-button>
      
      <!-- 圆形按钮 -->
      <uv-button 
        type="primary" 
        shape="circle"
        :customStyle="circleButtonStyle"
        icon="plus"
      />
    </view>
    
    <!-- 卡片组件 -->
    <view class="section">
      <text class="section-title">卡片组件</text>
      
      <view class="card-grid">
        <view class="product-card" v-for="product in products" :key="product.id">
          <image :src="product.image" class="product-image" />
          <view class="product-info">
            <text class="product-name">{{ product.name }}</text>
            <text class="product-price">¥{{ product.price }}</text>
            <view class="product-tags">
              <text class="tag" v-for="tag in product.tags" :key="tag">{{ tag }}</text>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 输入组件 -->
    <view class="section">
      <text class="section-title">输入组件</text>
      
      <uv-input 
        v-model="searchValue"
        placeholder="搜索商品..."
        :customStyle="searchInputStyle"
        prefixIcon="search"
        :clearable="true"
        @change="handleSearch"
      />
      
      <uv-textarea 
        v-model="commentValue"
        placeholder="请输入评论内容..."
        :customStyle="textareaStyle"
        :maxlength="200"
        :showWordLimit="true"
      />
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'

// 响应式数据
const searchValue = ref('')
const commentValue = ref('')
const products = ref([
  {
    id: 1,
    name: '精选商品名称',
    price: '99.00',
    image: '/static/images/product1.jpg',
    tags: ['热销', '包邮']
  }
])

// 按钮样式
const primaryButtonStyle = computed(() => ({
  backgroundColor: 'var(--primary-color)',
  borderRadius: 'var(--border-radius-button)',
  fontWeight: 'var(--font-weight-medium)',
  boxShadow: 'var(--shadow-light)'
}))

const secondaryButtonStyle = computed(() => ({
  backgroundColor: 'transparent',
  borderColor: 'var(--border-color)',
  borderRadius: 'var(--border-radius-button)',
  color: 'var(--text-primary)'
}))

const gradientButtonStyle = computed(() => ({
  background: 'var(--primary-gradient)',
  borderRadius: 'var(--border-radius-button)',
  fontWeight: 'var(--font-weight-medium)',
  boxShadow: 'var(--shadow-medium)'
}))

const circleButtonStyle = computed(() => ({
  backgroundColor: 'var(--primary-color)',
  width: '48px',
  height: '48px',
  borderRadius: '50%',
  boxShadow: 'var(--shadow-light)'
}))

// 输入框样式
const searchInputStyle = computed(() => ({
  backgroundColor: 'var(--background-secondary)',
  borderRadius: 'var(--border-radius-component)',
  border: 'none',
  padding: '12px 16px'
}))

const textareaStyle = computed(() => ({
  backgroundColor: 'var(--background-primary)',
  borderColor: 'var(--border-color)',
  borderRadius: 'var(--border-radius-component)',
  padding: '12px 16px'
}))

// 事件处理
const handlePrimaryAction = () => {
  console.log('主要操作')
}

const handleSecondaryAction = () => {
  console.log('次要操作')
}

const handleGradientAction = () => {
  console.log('渐变按钮操作')
}

const handleSearch = (value) => {
  console.log('搜索:', value)
}
</script>

<style lang="scss" scoped>
.ui-showcase {
  padding: var(--spacing-md);
}

.section {
  margin-bottom: var(--spacing-xl);
  
  .section-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    margin-bottom: var(--spacing-md);
    display: block;
  }
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-md);
}

.product-card {
  background: var(--background-primary);
  border-radius: var(--border-radius-card);
  box-shadow: var(--shadow-card);
  overflow: hidden;
  transition: transform var(--transition-normal);
  
  &:active {
    transform: scale(0.98);
  }
  
  .product-image {
    width: 100%;
    height: 120px;
    object-fit: cover;
  }
  
  .product-info {
    padding: var(--spacing-sm);
    
    .product-name {
      font-size: var(--font-size-md);
      font-weight: var(--font-weight-medium);
      color: var(--text-primary);
      display: block;
      margin-bottom: var(--spacing-xs);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .product-price {
      font-size: var(--font-size-lg);
      font-weight: var(--font-weight-semibold);
      color: var(--primary-color);
      display: block;
      margin-bottom: var(--spacing-xs);
    }
    
    .product-tags {
      display: flex;
      gap: var(--spacing-xs);
      
      .tag {
        font-size: var(--font-size-xs);
        color: var(--primary-color);
        background: var(--primary-light);
        padding: 2px 6px;
        border-radius: var(--border-radius-mini);
        opacity: 0.8;
      }
    }
  }
}
</style>
```

### 列表组件规范
```vue
<template>
  <view class="list-container">
    <!-- 下拉刷新 -->
    <uv-pull-refresh 
      v-model="refreshing" 
      @refresh="onRefresh"
      :customStyle="pullRefreshStyle"
    >
      <!-- 虚拟列表 -->
      <uv-list 
        @scrolltolower="onLoadMore"
        :customStyle="listStyle"
      >
        <!-- 列表项 -->
        <view 
          v-for="(item, index) in listData" 
          :key="item.id"
          class="list-item"
          :class="{ 'list-item-last': index === listData.length - 1 }"
          @click="handleItemClick(item)"
        >
          <view class="item-content">
            <image v-if="item.image" :src="item.image" class="item-image" />
            <view class="item-info">
              <text class="item-title">{{ item.title }}</text>
              <text class="item-subtitle">{{ item.subtitle }}</text>
              <view class="item-meta">
                <text class="item-time">{{ formatTime(item.createTime) }}</text>
                <text class="item-status" :class="`status-${item.status}`">
                  {{ getStatusText(item.status) }}
                </text>
              </view>
            </view>
            <view class="item-action">
              <uv-icon name="arrow-right" color="var(--text-tertiary)" size="16" />
            </view>
          </view>
        </view>
        
        <!-- 加载更多 -->
        <uv-load-more 
          :status="loadStatus"
          :customStyle="loadMoreStyle"
        />
      </uv-list>
    </uv-pull-refresh>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'
import { formatTime } from '@/utils/date'

const refreshing = ref(false)
const loading = ref(false)
const hasMore = ref(true)
const listData = ref([])

// 计算属性
const loadStatus = computed(() => {
  if (loading.value) return 'loading'
  if (!hasMore.value) return 'nomore'
  return 'loadmore'
})

// 样式配置
const pullRefreshStyle = computed(() => ({
  backgroundColor: 'var(--background-secondary)'
}))

const listStyle = computed(() => ({
  backgroundColor: 'var(--background-secondary)'
}))

const loadMoreStyle = computed(() => ({
  backgroundColor: 'var(--background-secondary)',
  color: 'var(--text-tertiary)'
}))

// 事件处理
const onRefresh = async () => {
  // 刷新逻辑
  refreshing.value = false
}

const onLoadMore = async () => {
  // 加载更多逻辑
}

const handleItemClick = (item) => {
  // 点击处理
}

const getStatusText = (status) => {
  const statusMap = {
    1: '进行中',
    2: '已完成',
    3: '已取消'
  }
  return statusMap[status] || '未知'
}
</script>

<style lang="scss" scoped>
.list-container {
  background: var(--background-secondary);
}

.list-item {
  background: var(--background-primary);
  margin: 0 var(--spacing-md) var(--spacing-sm);
  border-radius: var(--border-radius-component);
  box-shadow: var(--shadow-light);
  transition: transform var(--transition-fast);
  
  &:first-child {
    margin-top: var(--spacing-md);
  }
  
  &.list-item-last {
    margin-bottom: var(--spacing-md);
  }
  
  &:active {
    transform: scale(0.98);
  }
  
  .item-content {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    
    .item-image {
      width: 48px;
      height: 48px;
      border-radius: var(--border-radius-small);
      margin-right: var(--spacing-md);
      flex-shrink: 0;
    }
    
    .item-info {
      flex: 1;
      min-width: 0;
      
      .item-title {
        font-size: var(--font-size-md);
        font-weight: var(--font-weight-medium);
        color: var(--text-primary);
        display: block;
        margin-bottom: var(--spacing-xs);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .item-subtitle {
        font-size: var(--font-size-sm);
        color: var(--text-secondary);
        display: block;
        margin-bottom: var(--spacing-xs);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      
      .item-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        
        .item-time {
          font-size: var(--font-size-xs);
          color: var(--text-tertiary);
        }
        
        .item-status {
          font-size: var(--font-size-xs);
          padding: 2px 6px;
          border-radius: var(--border-radius-mini);
          
          &.status-1 {
            color: var(--info-color);
            background: rgba(25, 137, 250, 0.1);
          }
          
          &.status-2 {
            color: var(--success-color);
            background: rgba(7, 193, 96, 0.1);
          }
          
          &.status-3 {
            color: var(--text-tertiary);
            background: var(--background-tertiary);
          }
        }
      }
    }
    
    .item-action {
      margin-left: var(--spacing-sm);
      flex-shrink: 0;
    }
  }
}
</style>
```

## 🎨 主题定制系统

### 主题配置
```javascript
// theme/index.js
export const lightTheme = {
  colors: {
    primary: '#667eea',
    primaryLight: '#8fa4f3',
    primaryDark: '#4c63d2',
    success: '#07c160',
    warning: '#ff976a',
    error: '#ee0a24',
    info: '#1989fa',
    
    textPrimary: '#323233',
    textSecondary: '#646566',
    textTertiary: '#969799',
    textDisabled: '#c8c9cc',
    
    backgroundPrimary: '#ffffff',
    backgroundSecondary: '#f7f8fa',
    backgroundTertiary: '#ebedf0',
    
    borderColor: '#ebedf0',
    dividerColor: '#ebedf0'
  },
  
  spacing: {
    xs: '4px',
    sm: '8px',
    md: '16px',
    lg: '24px',
    xl: '32px',
    xxl: '48px'
  },
  
  borderRadius: {
    card: '16px',
    component: '12px',
    button: '8px',
    small: '6px',
    mini: '4px'
  },
  
  shadows: {
    light: '0 2px 12px rgba(100, 101, 102, 0.08)',
    medium: '0 4px 20px rgba(100, 101, 102, 0.12)',
    heavy: '0 8px 32px rgba(100, 101, 102, 0.16)',
    card: '0 2px 8px rgba(0, 0, 0, 0.06)'
  }
}

export const darkTheme = {
  colors: {
    primary: '#667eea',
    primaryLight: '#8fa4f3',
    primaryDark: '#4c63d2',
    success: '#07c160',
    warning: '#ff976a',
    error: '#ee0a24',
    info: '#1989fa',
    
    textPrimary: '#ffffff',
    textSecondary: '#c9c9c9',
    textTertiary: '#8c8c8c',
    textDisabled: '#5c5c5c',
    
    backgroundPrimary: '#1f1f1f',
    backgroundSecondary: '#2a2a2a',
    backgroundTertiary: '#3a3a3a',
    
    borderColor: '#3a3a3a',
    dividerColor: '#3a3a3a'
  },
  
  // 其他配置与light主题相同
  ...lightTheme
}

// 主题切换工具
export function applyTheme(theme) {
  const root = document.documentElement
  
  Object.entries(theme.colors).forEach(([key, value]) => {
    root.style.setProperty(`--${kebabCase(key)}`, value)
  })
  
  Object.entries(theme.spacing).forEach(([key, value]) => {
    root.style.setProperty(`--spacing-${key}`, value)
  })
  
  Object.entries(theme.borderRadius).forEach(([key, value]) => {
    root.style.setProperty(`--border-radius-${key}`, value)
  })
  
  Object.entries(theme.shadows).forEach(([key, value]) => {
    root.style.setProperty(`--shadow-${key}`, value)
  })
}

function kebabCase(str) {
  return str.replace(/([A-Z])/g, '-$1').toLowerCase()
}
```

## 📱 响应式设计规范

### 屏幕适配
```scss
// responsive.scss
// 屏幕尺寸断点
$breakpoints: (
  xs: 320px,
  sm: 375px,
  md: 414px,
  lg: 768px,
  xl: 1024px
);

// 响应式混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// 使用示例
.responsive-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-md);
  
  @include respond-to(sm) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @include respond-to(md) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  @include respond-to(lg) {
    grid-template-columns: repeat(4, 1fr);
  }
}

// 安全区域适配
.safe-area-container {
  padding-top: constant(safe-area-inset-top);
  padding-top: env(safe-area-inset-top);
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  padding-left: constant(safe-area-inset-left);
  padding-left: env(safe-area-inset-left);
  padding-right: constant(safe-area-inset-right);
  padding-right: env(safe-area-inset-right);
}
```

## 🎭 动画效果规范

### 过渡动画
```scss
// animations.scss
// 基础过渡
.fade-enter-active,
.fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 滑动过渡
.slide-up-enter-active,
.slide-up-leave-active {
  transition: transform var(--transition-normal);
}

.slide-up-enter-from {
  transform: translateY(100%);
}

.slide-up-leave-to {
  transform: translateY(100%);
}

// 缩放过渡
.scale-enter-active,
.scale-leave-active {
  transition: transform var(--transition-normal);
}

.scale-enter-from,
.scale-leave-to {
  transform: scale(0.8);
}

// 弹性动画
@keyframes bounce-in {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.bounce-in {
  animation: bounce-in 0.5s var(--animation-bounce);
}

// 加载动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-spin {
  animation: spin 1s linear infinite;
}
```

## 📋 UI设计最佳实践

### 1. 设计一致性
- 使用统一的设计令牌系统
- 保持组件样式的一致性
- 遵循品牌设计规范
- 建立完整的组件库

### 2. 用户体验
- 提供清晰的视觉反馈
- 合理的加载状态提示
- 流畅的页面过渡动画
- 友好的错误提示信息

### 3. 性能优化
- 合理使用CSS变量
- 避免复杂的CSS选择器
- 使用硬件加速的动画
- 优化图片和资源加载

### 4. 可访问性
- 保证足够的颜色对比度
- 提供合适的触摸目标大小
- 支持屏幕阅读器
- 考虑色盲用户的需求

### 5. 维护性
- 模块化的样式组织
- 清晰的命名规范
- 完善的文档说明
- 定期的设计审查

---

**文档说明**: 本文档为八闽助业集市C端小程序UI设计规范，基于现代卡片式设计理念和uv-ui组件库，所有UI开发必须严格遵循此规范。
