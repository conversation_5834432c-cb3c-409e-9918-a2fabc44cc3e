# 八闽助业集市管理后台UI组件使用规范

## 🎨 UI框架概述

### Ant Design Vue 1.7.2规范
```
┌─────────────────────────────────────────────────────────────────┐
│                    Ant Design Vue 1.7.2                        │
├─────────────────────────────────────────────────────────────────┤
│  基础组件    → Button、Input、Select等基础UI组件                │
│  布局组件    → Layout、Grid、Space等布局组件                    │
│  导航组件    → Menu、Breadcrumb、Pagination等导航组件           │
│  数据录入    → Form、DatePicker、Upload等表单组件               │
│  数据展示    → Table、List、Card等数据展示组件                  │
│  反馈组件    → Message、Modal、Notification等反馈组件           │
└─────────────────────────────────────────────────────────────────┘
```

### 设计原则
- **一致性**: 保持视觉和交互的一致性
- **可用性**: 界面简洁易用，操作流程清晰
- **美观性**: 符合现代设计美学，视觉层次分明
- **响应式**: 适配不同屏幕尺寸和设备

## 📝 表单组件规范

### a-form-model使用规范（重要）
```vue
<template>
  <div class="form-container">
    <!-- ✅ 正确：使用a-form-model -->
    <a-form-model
      ref="form"
      :model="form"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
    >
      <a-form-model-item label="用户名" prop="username">
        <a-input v-model="form.username" placeholder="请输入用户名" />
      </a-form-model-item>
      
      <a-form-model-item label="邮箱" prop="email">
        <a-input v-model="form.email" placeholder="请输入邮箱" />
      </a-form-model-item>
      
      <a-form-model-item label="状态" prop="status">
        <j-dict-select-tag v-model="form.status" dictCode="user_status" />
      </a-form-model-item>
      
      <a-form-model-item :wrapper-col="{ span: 14, offset: 4 }">
        <a-button type="primary" @click="handleSubmit">提交</a-button>
        <a-button style="margin-left: 10px" @click="handleReset">重置</a-button>
      </a-form-model-item>
    </a-form-model>
  </div>
</template>

<script>
export default {
  name: 'UserForm',
  data() {
    return {
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      form: {
        username: '',
        email: '',
        status: ''
      },
      rules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择状态', trigger: 'change' }
        ]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          // 表单验证通过，提交数据
          this.submitForm()
        } else {
          this.$message.error('请检查表单信息')
          return false
        }
      })
    },
    
    handleReset() {
      this.$refs.form.resetFields()
    },
    
    async submitForm() {
      try {
        const response = await this.$http.post('/api/user', this.form)
        if (response.success) {
          this.$message.success('保存成功')
          this.$emit('success')
        } else {
          this.$message.error(response.message || '保存失败')
        }
      } catch (error) {
        this.$message.error('保存失败')
      }
    }
  }
}
</script>
```

### 表单验证规范
```javascript
// 常用验证规则
const commonRules = {
  // 必填验证
  required: { required: true, message: '此项为必填项', trigger: 'blur' },
  
  // 用户名验证
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  
  // 邮箱验证
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  
  // 手机号验证
  phone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
  ],
  
  // 密码验证
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在6到20个字符', trigger: 'blur' }
  ],
  
  // 自定义验证函数
  customValidator: {
    validator: (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入内容'))
      } else if (value.length < 3) {
        callback(new Error('内容长度不能少于3个字符'))
      } else {
        callback()
      }
    },
    trigger: 'blur'
  }
}
```

## 📊 表格组件规范

### JeecgListMixin使用规范
```vue
<template>
  <div class="table-container">
    <!-- 查询区域 -->
    <div class="table-page-search-wrapper">
      <a-form-model layout="inline" @keyup.enter.native="searchQuery">
        <a-row :gutter="24">
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-model-item label="用户名">
              <a-input v-model="queryParam.username" placeholder="请输入用户名" />
            </a-form-model-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <a-form-model-item label="状态">
              <j-dict-select-tag v-model="queryParam.status" dictCode="user_status" placeholder="请选择状态" />
            </a-form-model-item>
          </a-col>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span class="table-page-search-submitButtons">
              <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
              <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
            </span>
          </a-col>
        </a-row>
      </a-form-model>
    </div>

    <!-- 操作按钮区域 -->
    <div class="table-operator">
      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>
      <a-button 
        type="primary" 
        icon="download" 
        @click="handleExportXls('用户列表')"
        :loading="exportLoading"
      >
        导出
      </a-button>
      <a-dropdown v-if="selectedRowKeys.length > 0">
        <a-menu slot="overlay">
          <a-menu-item key="1" @click="batchDel">
            <a-icon type="delete" />删除
          </a-menu-item>
        </a-menu>
        <a-button style="margin-left: 8px">
          批量操作 <a-icon type="down" />
        </a-button>
      </a-dropdown>
    </div>

    <!-- 表格区域 -->
    <div class="table-wrapper">
      <a-table
        ref="table"
        size="middle"
        :scroll="{ x: true }"
        bordered
        rowKey="id"
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <template slot="htmlSlot" slot-scope="text">
          <div v-html="text"></div>
        </template>
        
        <template slot="imgSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic;">无图片</span>
          <img v-else :src="getImgView(text)" height="25px" alt="" style="max-width: 80px" />
        </template>
        
        <template slot="fileSlot" slot-scope="text">
          <span v-if="!text" style="font-size: 12px; font-style: italic;">无文件</span>
          <a-button
            v-else
            :ghost="true"
            type="primary"
            icon="download"
            size="small"
            @click="downloadFile(text)"
          >
            下载
          </a-button>
        </template>

        <template slot="action" slot-scope="text, record">
          <a @click="handleEdit(record)">编辑</a>
          <a-divider type="vertical" />
          <a-dropdown>
            <a class="ant-dropdown-link">更多 <a-icon type="down" /></a>
            <a-menu slot="overlay">
              <a-menu-item>
                <a @click="handleDetail(record)">详情</a>
              </a-menu-item>
              <a-menu-item>
                <a-popconfirm title="确定删除吗?" @confirm="() => handleDelete(record.id)">
                  <a>删除</a>
                </a-popconfirm>
              </a-menu-item>
            </a-menu>
          </a-dropdown>
        </template>
      </a-table>
    </div>

    <!-- 表单弹窗 -->
    <user-modal ref="modalForm" @ok="modalFormOk" />
  </div>
</template>

<script>
import '@/assets/less/TableExpand.less'
import { mixinDevice } from '@/utils/mixin'
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import UserModal from './modules/UserModal'

export default {
  name: 'UserList',
  mixins: [JeecgListMixin, mixinDevice],
  components: {
    UserModal
  },
  data() {
    return {
      description: '用户管理页面',
      // 表格列定义
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: (text, record, index) => index + 1
        },
        {
          title: '用户名',
          align: 'center',
          dataIndex: 'username'
        },
        {
          title: '真实姓名',
          align: 'center',
          dataIndex: 'realname'
        },
        {
          title: '邮箱',
          align: 'center',
          dataIndex: 'email'
        },
        {
          title: '手机号',
          align: 'center',
          dataIndex: 'phone'
        },
        {
          title: '状态',
          align: 'center',
          dataIndex: 'status_dictText'
        },
        {
          title: '创建时间',
          align: 'center',
          dataIndex: 'createTime',
          sorter: true
        },
        {
          title: '操作',
          dataIndex: 'action',
          align: 'center',
          fixed: 'right',
          width: 147,
          scopedSlots: { customRender: 'action' }
        }
      ],
      url: {
        list: '/sys/user/list',
        delete: '/sys/user/delete',
        deleteBatch: '/sys/user/deleteBatch',
        exportXlsUrl: '/sys/user/exportXls',
        importExcelUrl: '/sys/user/importExcel'
      },
      dictOptions: {},
      superFieldList: []
    }
  },
  computed: {
    importExcelUrl() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    initDictConfig() {
      // 初始化字典配置
    },
    
    getSuperFieldList() {
      // 获取高级查询字段
    }
  }
}
</script>
```

## 🎛️ jeecg-boot组件使用规范

### j-dict-select-tag组件
```vue
<template>
  <div class="dict-example">
    <!-- 字典选择组件 -->
    <a-form-model-item label="用户状态" prop="status">
      <j-dict-select-tag 
        v-model="form.status" 
        dictCode="user_status" 
        placeholder="请选择状态"
        :disabled="false"
      />
    </a-form-model-item>
    
    <!-- 多选字典组件 -->
    <a-form-model-item label="用户角色" prop="roles">
      <j-dict-select-tag 
        v-model="form.roles" 
        dictCode="user_role" 
        placeholder="请选择角色"
        :multiple="true"
      />
    </a-form-model-item>
    
    <!-- 字典单选框组 -->
    <a-form-model-item label="性别" prop="gender">
      <j-dict-select-tag 
        v-model="form.gender" 
        dictCode="gender" 
        type="radio"
      />
    </a-form-model-item>
    
    <!-- 字典复选框组 -->
    <a-form-model-item label="兴趣爱好" prop="hobbies">
      <j-dict-select-tag 
        v-model="form.hobbies" 
        dictCode="hobbies" 
        type="checkbox"
      />
    </a-form-model-item>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        status: '',
        roles: [],
        gender: '',
        hobbies: []
      }
    }
  }
}
</script>
```

### j-upload组件
```vue
<template>
  <div class="upload-example">
    <!-- 单文件上传 -->
    <a-form-model-item label="头像" prop="avatar">
      <j-upload 
        v-model="form.avatar"
        :number="1"
        :isMultiple="false"
        :bizPath="bizPath"
        text="上传头像"
      />
    </a-form-model-item>
    
    <!-- 多文件上传 -->
    <a-form-model-item label="附件" prop="attachments">
      <j-upload 
        v-model="form.attachments"
        :number="5"
        :isMultiple="true"
        :bizPath="bizPath"
        text="上传附件"
      />
    </a-form-model-item>
    
    <!-- 图片上传 -->
    <a-form-model-item label="商品图片" prop="images">
      <j-image-upload 
        v-model="form.images"
        :number="9"
        :bizPath="bizPath"
      />
    </a-form-model-item>
  </div>
</template>

<script>
export default {
  data() {
    return {
      bizPath: 'user',
      form: {
        avatar: '',
        attachments: '',
        images: ''
      }
    }
  }
}
</script>
```

## 🔍 权限控制组件

### v-has指令使用
```vue
<template>
  <div class="permission-example">
    <!-- 按钮权限控制 -->
    <a-button v-has="'user:add'" type="primary" @click="handleAdd">
      新增用户
    </a-button>
    
    <a-button v-has="'user:edit'" @click="handleEdit">
      编辑用户
    </a-button>
    
    <a-button v-has="'user:delete'" type="danger" @click="handleDelete">
      删除用户
    </a-button>
    
    <!-- 菜单权限控制 -->
    <a-menu>
      <a-menu-item v-has="'dashboard'" key="dashboard">
        <a-icon type="dashboard" />
        <span>仪表盘</span>
      </a-menu-item>
      
      <a-sub-menu v-has="'user'" key="user">
        <span slot="title">
          <a-icon type="user" />
          <span>用户管理</span>
        </span>
        <a-menu-item v-has="'user:list'" key="user-list">用户列表</a-menu-item>
        <a-menu-item v-has="'user:role'" key="user-role">角色管理</a-menu-item>
      </a-sub-menu>
    </a-menu>
    
    <!-- 表格操作权限控制 -->
    <a-table :columns="columns" :dataSource="dataSource">
      <template slot="action" slot-scope="text, record">
        <a v-has="'user:view'" @click="handleView(record)">查看</a>
        <a-divider v-has="'user:edit'" type="vertical" />
        <a v-has="'user:edit'" @click="handleEdit(record)">编辑</a>
        <a-divider v-has="'user:delete'" type="vertical" />
        <a-popconfirm v-has="'user:delete'" title="确定删除吗?" @confirm="() => handleDelete(record.id)">
          <a>删除</a>
        </a-popconfirm>
      </template>
    </a-table>
  </div>
</template>
```

## 🎨 样式规范

### 主题色彩配置
```less
// src/assets/less/theme.less
@primary-color: #1890ff;          // 主色
@success-color: #52c41a;          // 成功色
@warning-color: #faad14;          // 警告色
@error-color: #f5222d;            // 错误色
@info-color: #1890ff;             // 信息色

@heading-color: rgba(0, 0, 0, 0.85);     // 标题色
@text-color: rgba(0, 0, 0, 0.65);        // 主文本色
@text-color-secondary: rgba(0, 0, 0, 0.45); // 次文本色
@disabled-color: rgba(0, 0, 0, 0.25);    // 失效色

@border-radius-base: 6px;         // 组件/浮层圆角
@border-color-base: #d9d9d9;      // 边框色
@box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15); // 浮层阴影
```

### 布局样式规范
```less
// 页面容器
.page-container {
  padding: 24px;
  background: #fff;
  min-height: calc(100vh - 64px);
}

// 表格容器
.table-container {
  .table-page-search-wrapper {
    padding: 16px;
    margin-bottom: 16px;
    background: #fafafa;
    border-radius: 6px;
    
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
  }
  
  .table-operator {
    margin-bottom: 16px;
    
    .ant-btn {
      margin-right: 8px;
      margin-bottom: 8px;
    }
  }
  
  .table-wrapper {
    .ant-table-thead > tr > th {
      background: #fafafa;
    }
  }
}

// 表单容器
.form-container {
  padding: 24px;
  background: #fff;
  border-radius: 6px;
  
  .ant-form-item {
    margin-bottom: 24px;
  }
  
  .form-actions {
    text-align: center;
    margin-top: 32px;
    
    .ant-btn {
      margin: 0 8px;
    }
  }
}

// 卡片样式
.card-container {
  .ant-card-head {
    border-bottom: 1px solid #e8e8e8;
  }
  
  .ant-card-body {
    padding: 24px;
  }
}
```

## 📱 响应式设计规范

### 断点配置
```less
// 响应式断点
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;

// 响应式栅格
.responsive-grid {
  @media (max-width: @screen-sm) {
    .ant-col {
      margin-bottom: 16px;
    }
  }
}

// 移动端适配
@media (max-width: @screen-md) {
  .table-page-search-wrapper {
    .ant-form-item {
      margin-bottom: 16px;
    }
    
    .table-page-search-submitButtons {
      text-align: center;
    }
  }
  
  .table-operator {
    text-align: center;
    
    .ant-btn {
      margin: 4px;
    }
  }
}
```

## 📋 UI组件使用最佳实践

### 1. 表单设计原则
- 使用a-form-model而非a-form
- 表单验证使用回调函数方式
- 合理使用字典组件简化开发
- 统一表单布局和样式

### 2. 表格设计原则
- 使用JeecgListMixin简化开发
- 合理设置表格列宽和固定列
- 提供完善的查询和操作功能
- 支持导入导出功能

### 3. 权限控制原则
- 使用v-has指令控制元素显示
- 在路由层面控制页面访问
- 在API层面控制数据权限
- 提供友好的无权限提示

### 4. 样式规范原则
- 遵循Ant Design设计语言
- 保持视觉风格一致性
- 合理使用主题色彩
- 支持响应式布局

### 5. 性能优化原则
- 合理使用组件懒加载
- 避免不必要的重新渲染
- 优化表格数据展示
- 使用虚拟滚动处理大数据量

---

**文档说明**: 本文档为八闽助业集市管理后台UI组件使用规范，所有UI开发必须严格遵循此规范。
