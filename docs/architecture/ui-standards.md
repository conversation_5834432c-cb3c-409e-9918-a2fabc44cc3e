# 八闽助业集市H5端UI设计规范

## 🎨 H5端设计系统

### 设计理念
```
┌─────────────────────────────────────────────────────────────────┐
│                    H5端设计原则                                 │
├─────────────────────────────────────────────────────────────────┤
│  响应式设计  → 适配多种屏幕尺寸和设备类型                        │
│  触摸优化    → 针对触摸操作优化交互体验                         │
│  微信融合    → 与微信生态深度融合的视觉体验                      │
│  性能优先    → 优化加载速度和运行性能                           │
│  一致性      → 与C端小程序保持视觉一致性                        │
└─────────────────────────────────────────────────────────────────┘
```

### H5端设计令牌系统
```scss
// h5-design-tokens.scss
:root {
  /* ========== 继承C端基础设计令牌 ========== */
  --primary-color: #667eea;
  --primary-light: #8fa4f3;
  --primary-dark: #4c63d2;
  --success-color: #07c160;
  --warning-color: #ff976a;
  --error-color: #ee0a24;
  --info-color: #1989fa;
  
  --text-primary: #323233;
  --text-secondary: #646566;
  --text-tertiary: #969799;
  --background-primary: #ffffff;
  --background-secondary: #f7f8fa;
  
  --border-radius-card: 16px;
  --border-radius-component: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.06);
  
  /* ========== H5端专用设计令牌 ========== */
  --h5-header-height: 44px;
  --h5-navbar-height: 50px;
  --h5-tabbar-height: 50px;
  --h5-safe-area-top: env(safe-area-inset-top);
  --h5-safe-area-bottom: env(safe-area-inset-bottom);
  
  /* ========== 响应式断点 ========== */
  --breakpoint-xs: 320px;
  --breakpoint-sm: 375px;
  --breakpoint-md: 414px;
  --breakpoint-lg: 768px;
  --breakpoint-xl: 1024px;
  
  /* ========== 触摸目标尺寸 ========== */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
  --touch-target-large: 56px;
  
  /* ========== 微信主题色彩 ========== */
  --wechat-green: #07c160;
  --wechat-green-light: rgba(7, 193, 96, 0.1);
  --wechat-blue: #1aad19;
  --wechat-orange: #fa9d3b;
  --wechat-red: #e64340;
  
  /* ========== Z-index层级 ========== */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;
  --z-index-toast: 1080;
}
```

## 📱 响应式布局组件

### 响应式容器组件
```vue
<template>
  <view class="h5-responsive-layout" :class="layoutClass" :style="layoutStyle">
    <!-- 顶部导航栏 -->
    <view class="layout-header" v-if="showHeader" :style="headerStyle">
      <slot name="header">
        <view class="default-header">
          <view class="header-left">
            <button v-if="showBack" @click="handleBack" class="back-button">
              <uv-icon name="arrow-left" size="20" />
            </button>
          </view>
          <view class="header-center">
            <text class="header-title">{{ title }}</text>
          </view>
          <view class="header-right">
            <slot name="headerRight"></slot>
          </view>
        </view>
      </slot>
    </view>
    
    <!-- 主要内容区域 -->
    <view class="layout-content" :style="contentStyle">
      <slot></slot>
    </view>
    
    <!-- 底部导航栏 -->
    <view class="layout-footer" v-if="showFooter" :style="footerStyle">
      <slot name="footer"></slot>
    </view>
    
    <!-- 浮动操作按钮 -->
    <view class="layout-fab" v-if="$slots.fab" :style="fabStyle">
      <slot name="fab"></slot>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  title: String,
  showHeader: {
    type: Boolean,
    default: true
  },
  showBack: {
    type: Boolean,
    default: false
  },
  showFooter: {
    type: Boolean,
    default: false
  },
  headerColor: {
    type: String,
    default: 'var(--background-primary)'
  },
  footerColor: {
    type: String,
    default: 'var(--background-primary)'
  },
  safeArea: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['back'])

// 响应式数据
const screenWidth = ref(0)
const screenHeight = ref(0)

// 计算属性
const layoutClass = computed(() => {
  const classes = ['h5-responsive-layout']
  
  if (screenWidth.value >= 768) {
    classes.push('layout-tablet')
  }
  
  if (screenWidth.value >= 1024) {
    classes.push('layout-desktop')
  }
  
  if (props.safeArea) {
    classes.push('layout-safe-area')
  }
  
  return classes
})

const layoutStyle = computed(() => ({
  '--screen-width': `${screenWidth.value}px`,
  '--screen-height': `${screenHeight.value}px`
}))

const headerStyle = computed(() => ({
  backgroundColor: props.headerColor,
  height: 'var(--h5-header-height)',
  paddingTop: props.safeArea ? 'var(--h5-safe-area-top)' : '0'
}))

const contentStyle = computed(() => {
  const paddingTop = props.showHeader ? 'var(--h5-header-height)' : '0'
  const paddingBottom = props.showFooter ? 'var(--h5-tabbar-height)' : '0'
  
  return {
    paddingTop: props.safeArea ? `calc(${paddingTop} + var(--h5-safe-area-top))` : paddingTop,
    paddingBottom: props.safeArea ? `calc(${paddingBottom} + var(--h5-safe-area-bottom))` : paddingBottom,
    minHeight: `calc(100vh - ${paddingTop} - ${paddingBottom})`
  }
})

const footerStyle = computed(() => ({
  backgroundColor: props.footerColor,
  height: 'var(--h5-tabbar-height)',
  paddingBottom: props.safeArea ? 'var(--h5-safe-area-bottom)' : '0'
}))

const fabStyle = computed(() => ({
  bottom: props.safeArea ? 
    `calc(var(--spacing-lg) + var(--h5-safe-area-bottom))` : 
    'var(--spacing-lg)'
}))

// 事件处理
const handleBack = () => {
  emit('back')
  if (window.history.length > 1) {
    window.history.back()
  } else {
    window.location.href = '/'
  }
}

// 更新屏幕尺寸
const updateScreenSize = () => {
  screenWidth.value = window.innerWidth
  screenHeight.value = window.innerHeight
}

// 生命周期
onMounted(() => {
  updateScreenSize()
  window.addEventListener('resize', updateScreenSize)
  window.addEventListener('orientationchange', updateScreenSize)
})

onUnmounted(() => {
  window.removeEventListener('resize', updateScreenSize)
  window.removeEventListener('orientationchange', updateScreenSize)
})
</script>

<style lang="scss" scoped>
.h5-responsive-layout {
  position: relative;
  min-height: 100vh;
  background: var(--background-secondary);
  
  // 平板样式
  &.layout-tablet {
    max-width: 768px;
    margin: 0 auto;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  }
  
  // 桌面样式
  &.layout-desktop {
    max-width: 414px;
    margin: 0 auto;
    background: var(--background-tertiary);
    
    .layout-content {
      background: var(--background-primary);
    }
  }
  
  // 安全区域适配
  &.layout-safe-area {
    .layout-header {
      padding-top: var(--h5-safe-area-top);
    }
    
    .layout-footer {
      padding-bottom: var(--h5-safe-area-bottom);
    }
  }
}

.layout-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-fixed);
  border-bottom: 1px solid var(--border-color-light);
  
  .default-header {
    display: flex;
    align-items: center;
    height: var(--h5-header-height);
    padding: 0 var(--spacing-md);
    
    .header-left,
    .header-right {
      flex: 0 0 auto;
      width: 60px;
    }
    
    .header-center {
      flex: 1;
      text-align: center;
      
      .header-title {
        font-size: var(--font-size-lg);
        font-weight: var(--font-weight-semibold);
        color: var(--text-primary);
      }
    }
    
    .back-button {
      width: var(--touch-target-min);
      height: var(--touch-target-min);
      border-radius: 50%;
      border: none;
      background: transparent;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-primary);
      transition: background-color var(--transition-fast);
      
      &:active {
        background: var(--background-secondary);
      }
    }
  }
}

.layout-content {
  position: relative;
  z-index: 1;
}

.layout-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-fixed);
  border-top: 1px solid var(--border-color-light);
}

.layout-fab {
  position: fixed;
  right: var(--spacing-md);
  z-index: var(--z-index-fixed);
}

// 响应式媒体查询
@media (max-width: 375px) {
  .h5-responsive-layout {
    .default-header {
      padding: 0 var(--spacing-sm);
    }
  }
}

@media (min-width: 768px) {
  .h5-responsive-layout {
    &.layout-tablet {
      border-radius: var(--border-radius-card);
      margin-top: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
      overflow: hidden;
    }
  }
}

@media (min-width: 1024px) {
  .h5-responsive-layout {
    &.layout-desktop {
      margin-top: var(--spacing-xxl);
      margin-bottom: var(--spacing-xxl);
      
      .layout-content {
        border-radius: var(--border-radius-card);
        box-shadow: var(--shadow-elevated);
      }
    }
  }
}
</style>
```

### 微信风格按钮组件
```vue
<template>
  <button 
    class="wechat-button" 
    :class="buttonClass" 
    :style="buttonStyle"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <view class="button-content">
      <uv-loading v-if="loading" size="16" color="currentColor" />
      <uv-icon v-else-if="icon" :name="icon" :size="iconSize" />
      <text class="button-text" v-if="$slots.default || text">
        <slot>{{ text }}</slot>
      </text>
    </view>
  </button>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'default',
    validator: value => ['default', 'primary', 'success', 'warning', 'danger', 'wechat'].includes(value)
  },
  size: {
    type: String,
    default: 'default',
    validator: value => ['mini', 'small', 'default', 'large'].includes(value)
  },
  text: String,
  icon: String,
  loading: Boolean,
  disabled: Boolean,
  plain: Boolean,
  round: Boolean,
  block: Boolean
})

const emit = defineEmits(['click'])

// 计算属性
const buttonClass = computed(() => {
  const classes = [`wechat-button-${props.type}`, `wechat-button-${props.size}`]
  
  if (props.plain) classes.push('wechat-button-plain')
  if (props.round) classes.push('wechat-button-round')
  if (props.block) classes.push('wechat-button-block')
  if (props.disabled) classes.push('wechat-button-disabled')
  if (props.loading) classes.push('wechat-button-loading')
  
  return classes
})

const buttonStyle = computed(() => {
  const styles = {}
  
  // 微信绿色主题
  if (props.type === 'wechat') {
    if (props.plain) {
      styles.color = 'var(--wechat-green)'
      styles.borderColor = 'var(--wechat-green)'
      styles.backgroundColor = 'transparent'
    } else {
      styles.backgroundColor = 'var(--wechat-green)'
      styles.borderColor = 'var(--wechat-green)'
      styles.color = 'white'
    }
  }
  
  return styles
})

const iconSize = computed(() => {
  const sizeMap = {
    mini: 12,
    small: 14,
    default: 16,
    large: 18
  }
  return sizeMap[props.size]
})

// 事件处理
const handleClick = (event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style lang="scss" scoped>
.wechat-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-small);
  font-weight: var(--font-weight-medium);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-fast);
  user-select: none;
  outline: none;
  
  &:active {
    transform: scale(0.98);
  }
  
  // 尺寸变体
  &.wechat-button-mini {
    height: 28px;
    padding: 0 var(--spacing-sm);
    font-size: var(--font-size-xs);
    min-width: var(--touch-target-min);
  }
  
  &.wechat-button-small {
    height: 36px;
    padding: 0 var(--spacing-md);
    font-size: var(--font-size-sm);
    min-width: var(--touch-target-min);
  }
  
  &.wechat-button-default {
    height: var(--touch-target-comfortable);
    padding: 0 var(--spacing-lg);
    font-size: var(--font-size-md);
    min-width: var(--touch-target-comfortable);
  }
  
  &.wechat-button-large {
    height: var(--touch-target-large);
    padding: 0 var(--spacing-xl);
    font-size: var(--font-size-lg);
    min-width: var(--touch-target-large);
  }
  
  // 类型变体
  &.wechat-button-default {
    background: var(--background-primary);
    color: var(--text-primary);
    border-color: var(--border-color);
    
    &:hover {
      background: var(--background-secondary);
    }
  }
  
  &.wechat-button-primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    
    &:hover {
      background: var(--primary-dark);
    }
  }
  
  &.wechat-button-success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
  }
  
  &.wechat-button-warning {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
  }
  
  &.wechat-button-danger {
    background: var(--error-color);
    color: white;
    border-color: var(--error-color);
  }
  
  // 朴素按钮
  &.wechat-button-plain {
    background: transparent;
    
    &.wechat-button-primary {
      color: var(--primary-color);
      border-color: var(--primary-color);
    }
    
    &.wechat-button-success {
      color: var(--success-color);
      border-color: var(--success-color);
    }
    
    &.wechat-button-warning {
      color: var(--warning-color);
      border-color: var(--warning-color);
    }
    
    &.wechat-button-danger {
      color: var(--error-color);
      border-color: var(--error-color);
    }
  }
  
  // 圆角按钮
  &.wechat-button-round {
    border-radius: 999px;
  }
  
  // 块级按钮
  &.wechat-button-block {
    width: 100%;
    display: flex;
  }
  
  // 禁用状态
  &.wechat-button-disabled {
    opacity: 0.6;
    cursor: not-allowed;
    
    &:active {
      transform: none;
    }
  }
  
  // 加载状态
  &.wechat-button-loading {
    cursor: default;
    
    &:active {
      transform: none;
    }
  }
}

.button-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  
  .button-text {
    color: inherit;
  }
}
</style>
```

## 🎨 微信风格卡片组件

### 微信风格列表卡片
```vue
<template>
  <view class="wechat-card" :class="cardClass" @click="handleClick">
    <view class="card-header" v-if="$slots.header || title">
      <slot name="header">
        <text class="card-title">{{ title }}</text>
        <text class="card-subtitle" v-if="subtitle">{{ subtitle }}</text>
      </slot>
    </view>
    
    <view class="card-body">
      <slot></slot>
    </view>
    
    <view class="card-footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </view>
    
    <view class="card-arrow" v-if="showArrow">
      <uv-icon name="arrow-right" size="16" color="var(--text-tertiary)" />
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  title: String,
  subtitle: String,
  showArrow: {
    type: Boolean,
    default: false
  },
  clickable: {
    type: Boolean,
    default: false
  },
  shadow: {
    type: String,
    default: 'light',
    validator: value => ['none', 'light', 'medium', 'heavy'].includes(value)
  }
})

const emit = defineEmits(['click'])

// 计算属性
const cardClass = computed(() => {
  const classes = []
  
  if (props.clickable) classes.push('wechat-card-clickable')
  if (props.shadow !== 'none') classes.push(`wechat-card-shadow-${props.shadow}`)
  
  return classes
})

// 事件处理
const handleClick = (event) => {
  if (props.clickable) {
    emit('click', event)
  }
}
</script>

<style lang="scss" scoped>
.wechat-card {
  position: relative;
  background: var(--background-primary);
  border-radius: var(--border-radius-card);
  overflow: hidden;
  margin-bottom: var(--spacing-sm);
  
  &.wechat-card-clickable {
    cursor: pointer;
    transition: transform var(--transition-fast);
    
    &:active {
      transform: scale(0.98);
    }
  }
  
  &.wechat-card-shadow-light {
    box-shadow: var(--shadow-card);
  }
  
  &.wechat-card-shadow-medium {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  }
  
  &.wechat-card-shadow-heavy {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }
}

.card-header {
  padding: var(--spacing-md) var(--spacing-md) 0;
  
  .card-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--text-primary);
    display: block;
    margin-bottom: var(--spacing-xs);
  }
  
  .card-subtitle {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
    display: block;
  }
}

.card-body {
  padding: var(--spacing-md);
}

.card-footer {
  padding: 0 var(--spacing-md) var(--spacing-md);
  border-top: 1px solid var(--border-color-light);
  margin-top: var(--spacing-md);
  padding-top: var(--spacing-md);
}

.card-arrow {
  position: absolute;
  right: var(--spacing-md);
  top: 50%;
  transform: translateY(-50%);
}
</style>
```

## 📋 H5端UI设计最佳实践

### 1. 响应式设计
- 使用CSS媒体查询适配不同屏幕
- 合理设置触摸目标大小（最小44px）
- 考虑横竖屏切换的布局调整
- 支持多种设备类型的显示

### 2. 微信生态融合
- 遵循微信设计规范
- 使用微信风格的交互模式
- 合理使用微信绿色主题
- 优化微信浏览器兼容性

### 3. 性能优化
- 使用CSS3硬件加速
- 合理使用图片懒加载
- 优化动画性能
- 减少重绘和回流

### 4. 用户体验
- 提供清晰的视觉反馈
- 合理的加载状态提示
- 友好的错误处理
- 流畅的页面过渡

### 5. 可访问性
- 保证足够的颜色对比度
- 支持屏幕阅读器
- 合理的焦点管理
- 键盘导航支持

---

**文档说明**: 本文档为八闽助业集市H5端UI设计规范，专注于微信公众号和H5环境的界面设计和用户体验优化，确保在各种设备和浏览器中的一致性表现。
